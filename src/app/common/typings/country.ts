import { BaseApiObject } from './base';

export interface Country extends BaseApiObject {
  code: string;
  displayName: string;
}

export interface InheritedCountryCode {
  code: string;
  inherited: boolean;
}

export function getInheritedCountryCode(ownCountries: string[], inheritedCountries: string[]): InheritedCountryCode[] {
  const result = inheritedCountries?.reduce<Record<string, boolean>>((result, code) => ({
    ...result,
    [code]: true
  }), {}) || {};
  for (const code of ownCountries ?? []) {
    result[code] = false;
  }
  return Object.entries(result).map<InheritedCountryCode>(([code, inherited]) => ({ code, inherited }));
}
