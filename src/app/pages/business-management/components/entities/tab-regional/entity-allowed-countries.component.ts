import { ChangeDetectionStrategy, ChangeDetectorRef, Component } from '@angular/core';
import { EntityCountriesComponent, EntityCountry } from './entity-countries.component';
import { PERMISSIONS_NAMES, RowAction, SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { filter, finalize, map, mergeMap, switchMap, take, tap } from 'rxjs/operators';
import { EntityService, pushDefaultToStart } from 'src/app/common/services/entity.service';
import { Entity } from 'src/app/common/models/entity.model';
import { CountryItem } from '../../mat-business-structure/dialogs/mat-country-dialog/mat-country-dialog.component';
import { CountryService } from 'src/app/common/services/country.service';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { Country } from 'src/app/common/typings';

@Component({
  selector: '[entity-allowed-countries]',
  templateUrl: './entity-countries.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EntityAllowedCountriesComponent extends EntityCountriesComponent {
  title = 'ENTITY_SETUP.REGIONAL.titleAllowedCountries';

  private readonly disableDelete: boolean;

  constructor(
    private countryService: CountryService<Country>,
    private entityService: EntityService<Entity>,
    dialog: MatDialog,
    private notifications: SwuiNotificationsService,
    private translate: TranslateService,
    swHubAuthService: SwHubAuthService,
    cdr: ChangeDetectorRef,
  ) {
    super(dialog, cdr);
    this.disableDelete = !swHubAuthService.allowedTo([PERMISSIONS_NAMES.COUNTRY_REMOVE]);
  }

  showCountryModal($event: Event): void {
    super.showCountryModal($event);
    this.buildCountriesModal('allowed').pipe(
      filter((data: { entity: Entity, selected: string[] }) => !!data && typeof data.entity !== 'undefined'),
      map(({ entity, selected }) => {
        const updated = new Entity(entity);
        updated.countries = [...selected];
        return updated;
      }),
      switchMap((entity: Entity) => this.entityService.updateEntityItem(entity)),
      tap((entity: Entity) => this.entity.update(entity)),
      tap(() => this.notifications.success(
        this.translate.instant('ENTITY_SETUP.REGIONAL.MODALS.notificationCountriesAdded'))),
      finalize(() => this.refreshData()),
      take(1)
    ).subscribe();
  }

  removeCountryModal(countryCode: string) {
    this.buildRemoveCountryModal(countryCode).pipe(
      filter(code => !!code && code in this.countriesHash),
      mergeMap((code) => this.countryService.deleteCountry(this.entity.path, code)),
      tap(() => this.notifications.success(
        this.translate.instant('ENTITY_SETUP.REGIONAL.MODALS.notificationCountryRemoved'))
      ),
      take(1),
      finalize(() => this.refreshData())
    ).subscribe(() => {
      this.entity.countries = this.entity.countries.filter(item => item !== countryCode)
    });
  }

  protected loadData(): EntityCountry[] {
    const countries = pushDefaultToStart(this.entity.countries, this.entity.defaultCountry);
    return countries.map<EntityCountry>((code) => ({
      code,
      displayName: this.countriesHash[code]?.displayName ?? '',
      isDefault: this.entity.defaultCountry === code,
      isInherited: false
    }));
  }

  protected buildAvailableCountries(): CountryItem[] {
    let countryList = this.entity.entityParent.countries;
    let rootParent = this.entity.entityParent.isRoot();
    let rootCountriesMismatch = countryList.length !== this.countries.length;

    if (rootParent && rootCountriesMismatch) {
      countryList = Object.keys(this.countriesHash);
    }
    return countryList.map((code) => {
      const item = new CountryItem(this.countriesHash[code]);
      item.selected = this.entity.countries.indexOf(code) > -1;
      return item;
    });
  }

  protected setRowActions() {
    this.rowActions = [
      new RowAction({
        title: 'ENTITY_SETUP.REGIONAL.actionDelete',
        icon: 'delete',
        inMenu: true,
        fn: ({ code }) => this.removeCountryModal(code),
        canActivateFn: () => !this.disabledList && !this.disableDelete
      }),
      new RowAction({
        title: 'ENTITY_SETUP.REGIONAL.setDefault',
        icon: 'home',
        fn: ({ code }) => this.setDefaultCountry(code),
        canActivateFn: ({ isDefault }) => !isDefault && !this.disabledList,
      })
    ];
  }

  private setDefaultCountry(defaultCountry: string) {
    this.entity.defaultCountry = defaultCountry;
    this.entityService.updateEntityItem(this.entity)
      .pipe(
        take(1),
        finalize(() => this.refreshData())
      ).subscribe((data) => {
        this.entity.update(data);
      });
  }
}
