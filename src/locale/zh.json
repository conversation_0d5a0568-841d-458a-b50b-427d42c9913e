{"ALL": {"IN_PROGRESS": "进行中······", "REFRESH_SESSION": "您的会话将在{{timeToExpire}}内过期。您是否要保持登录状态？", "SERVICE_IS_UNAVAILABLE": "服务不可用", "SERVICE_IS_UNAVAILABLE_DESC": "请联系技术支持", "MULTIPLIER": "翻倍乘数", "ALL": "All", "DEFAULT_SCHEMA_SECTIONS": {"basic": "基本信息", "operatorAgent": "运营商/代理商", "balance": "余额", "financial": "财务", "other": "其他", "dateTime": "日期/时间"}, "confirm": "应用", "save": "保存", "decline": "取消", "copyToClipboard": "Copy to clipboard", "noItemsFound": "未找到任何条目", "itemsFound": "找到条目{{value}}", "noData": "无数据", "urlFormat": "URL格式：http(s)://example.com", "reset": "重置", "resetDefault": "恢复默认设置", "delete": "Delete", "search": "搜索", "title": "标题", "description": "描述", "key": "密钥", "reseller": "转销商", "operator": "运营商", "merchant": "商家", "liveStudio": "Live Studio", "skywind": "Skywind", "inherited": "已继承", "active": "已激活", "inactive": "未激活", "inactive_keep": "未激活 (Keep active sessions alive)", "inactive_kill": "未激活 (Kill active sessions)", "test": "测试", "hidden": "Hidden", "pleaseSelect": "请选择……", "settings": "设置", "code": "代码", "available": "{{numberOfItems}}可得", "found": "已找到{{numberOfItems}}", "selected": "已选{{numberOfItems}}", "selectAll": "全选", "resellerOperator": "转销商/运营商", "general": "通用", "games": "游戏", "more": "more", "dontShowAgain": "Do not show again"}, "CHART": {"X-AXIS-TITLE-DEFAULT": "金额"}, "DASHBOARD": {"AVERAGE_PAYMENTS": "平均付款", "GGR": "GGR", "GGR (EUR)": "GGR (欧元)", "PAYMENTS": "付款", "TITLE": "总结报告", "TOP_GAMES": "人气游戏", "Total Bet (EUR)": "总投注 (欧元)", "Total Win (EUR)": "总奖金 (欧元)"}, "DIALOG": {"decline": "拒绝", "no": "否", "ok": "好的", "yes": "是", "back": "背部", "next": "下一个", "previous": "以前", "close": "关闭", "cancel": "取消", "save": "保存", "saveChanges": "保存修改", "сomplete": "完成", "activate": "激活", "deactivate": "Deactivate", "active": "已激活", "inactive": "未激活", "finish": "完成"}, "FILTER": {"FINISHED": {"finished": "已结束", "unfinished": "未结束"}, "newFilter": "新筛选条件", "noFilterData": "查找不到符合筛选条件的数据", "noSavedFilters": "无已保存的筛选条件", "rangeFrom": "开始日期", "rangeTo": "结束日期", "reset": "重置", "search": "搜索", "currencyrangePlaceholder": "请选择货币…", "resellerOperatorRequired": "转销商/运营商（必填项）", "resellerOperator": "转销商/运营商", "masterAll": "MASTER - ALL"}, "FORGOTPASSWORD": {"title": "忘记密码", "desc": "请输入您的用户名或邮箱地址，我们将会给您发送一封包含您个人信息的邮件，说明重置密码的方法。", "username": "用户名或邮箱", "submit": "发送请求", "success-notification": "The reset link has been sent to your email. Please follow the link to proceed the password reset.", "to-login": "To login page", "notificationReset": "Password was successfully reset"}, "MENU_SECTIONS": {"dashboard": "仪表板", "businessManagement": "商业管理", "businessManagement_structure": "商业结构", "businessManagement_transfers": "转账", "businessManagement_agents": "代理商", "businessManagement_entities": "实体", "businessManagement_entitySetup": "实体设置", "users": "用户", "usersList": "用户列表", "userRoles": "角色", "usersActivityLog": "活动日志", "customers": "玩家", "payments": "付款", "paymentsDeposits": "定金", "paymentsWithdrawals": "取款", "paymentsTransfers": "转账", "paymentsTransfpaymentGroupsers": "付款群组", "marketing": "营销", "marketingPromotions": "推广", "marketingMaterials": "市场营销材料", "reports": "报告", "reportsCurrency": "货币报告", "reportsFinancial": "财务报告", "reportsPlayers": "玩家报告", "reportsKPI": "KPI报告", "reportsRG": "自愿禁赌与暂停", "lobbies": "大厅", "lobbiesLayouts": "大厅布局", "retail": "零售", "retailTerminals": "终端", "games": "游戏", "gamesHistory": "游戏历史", "externalGamesHistory": "外部游戏历史", "gamesLobbies": "大厅", "gamesLobbiesManagement": "大厅管理", "gamesManagement": "游戏可得性", "gamesCategoriesManagement": "游戏类别", "support": "支持", "supportFAQ": "常见问题解答", "supportSupportTickets": "支持工单", "supportSystemNotifications": "系统通知", "settings": "设置", "settings_general": "通用", "settings_domains": "域名", "settings_grc": "豪赏会", "settings_jurisdictions": "Jurisdictions", "settings_labels": "Labels Management", "settings_gameserver": "游戏服务器", "settings_proxy": "代理", "bulkActions": "实体批量操作", "financialReport": "财务报告", "integrations": "整合", "cashier": "收银台", "gameManagement": "Games management", "liveLimits": "Live Limits", "bi-reports-switch": "BI reports switch", "id-transcode": "ID Transcode"}, "CASHIER": {"playerUsername": "Player username", "amount": "Amount", "createPlayer": "Create Player", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "cashout": "Cashout", "seePlayerInfo": "See player info", "balance": "Balance", "customerID": "玩家ID", "firstName": "First Name", "lastName": "Last Name", "login": "<PERSON><PERSON>", "email": "E-mail", "currency": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "type": "Type", "profileUpdate": "Profile Update", "lastLogin": "Last Login", "registered": "Registered", "country": "Country/City", "lookingForPlayer": "Looking for a player", "playerNotFound": "Player not found"}, "CUSTOMERS": {"playerIsOnline": "Withdrawal cannot be processed for online players", "setActive": "设置为已激活状态", "setInactive": "设置为未激活状态", "defaultGameGroupLabel": "\"{{ defaultGameGroup }}\" game group will be used", "defaultLimitsLabel": "Default limits will be applied to this player (because of empty game group)", "GRID": {"contentProvider": "内容提供者", "contentProvider_placeholder": "进行中······", "operator": "运营商", "operator_scope": "运营商/代理商", "agentDomain": "代理商", "agentDomain_scope": "运营商/代理商", "code": "玩家ID", "login": "登录", "firstName": "名", "lastName": "姓", "username": "用户名", "username_placeholder": "进行中……", "email": "邮箱", "language": "语言", "currency": "货币", "currency_scope": "余额", "balance": "余额", "balance_scope": "余额", "balances": "余额", "balances_scope": "余额", "balanceRange": "余额范围", "balanceRange_placeholder": "进行中······", "balanceRange_scope": "余额", "hasDeposits": "拥有余额", "hasDeposits_placeholder": "进行中······", "hasDeposits_scope": "存款", "hasWithdrawals": "取款", "hasWithdrawals_placeholder": "进行中······", "hasWithdrawals_scope": "余额", "provider": "PMT群组", "status": "状态", "isTest": "类型", "paymentDateHour": "Payment Date", "isTest_placeholder": "类型", "createdAt": "注册日期", "createdAt_scope": "日期/时间", "lastLogin": "上次登录", "lastLogin_scope": "日期/时间", "country": "国家", "country_scope": "其他", "isOnline": "在线状态", "isOnline_scope": "其他", "registrationIP": "注册IP", "registrationIP_scope": "其他", "createPlayer": "创建玩家", "resellerOperator": "转销商/运营商", "gameGroup": "Game limits group"}, "MODAL": {"createPlayer": "创建玩家", "btnClose": "关闭", "btnApply": "应用", "btnSave": "保存修改", "create": "创建用户", "id": "玩家ID", "email": "电子邮箱", "password": "密码", "name": "名", "lastName": "姓", "status": "状态", "active": "已激活", "inactive": "未激活", "country": "国家", "language": "语言", "currency": "货币", "promoFreebetTitle": "应用免费投注推广活动", "promoBonusCoinTitle": "应用奖励金币推广活动", "promotion": "推广", "code": "代码", "result": "结果"}, "LABEL": {"testAccount": "测试", "realAccount": "真实", "userOnline": "在线", "userOffline": "离线", "userActive": "活跃", "userInactive": "非活跃", "userSuspended": "暂停", "unblocked": "Unblocked", "blocked": "Blocked"}, "DETAILS": {"type": "类别", "editPlayer": "编辑玩家{{player}}", "withdrawal_password": "取款密码", "id": "ID", "registered": "已注册", "profile_update": "更新个人资料", "account": "账户", "save": "保存", "back": "返回", "blockedStatus": "Blocked status", "CUSTOMFIELDS": {"phone": "电话", "postal_code": "邮政编码", "province": "省份", "city": "城市", "address": "地址", "date_of_birth": "出生日期"}, "BALANCE": {"transfer": "转账", "amount": "数额", "close": "关闭", "in": "入", "out": "出", "transferIn": "Transfer in", "transferOut": "Transfer out"}, "TABS": {"general": "通用", "payments": "支付", "promotions": "推广", "game_history": "游戏历史", "responsible_gaming": "负责任游戏"}, "VIEWSTATE": {"Normal": "正常", "Expanded": "扩展", "Full screen": "全屏", "fullscreen": "全屏", "newTab": "在新标签页中打开"}}, "PAYMENTS": {"TABS": {"deposits": "存款", "withdrawals": "取款"}}, "NOTIFICATIONS": {"status_inactive": "状态已成功设置为“已激活”", "status_active": "状态已成功设置为“未激活”", "type_real": "类别已成功设置为“真实”", "type_test": "类别已成功设置为“测试”", "not_set": "未设置", "transfer_success": "转账成功！", "edit_success": "已成功编辑玩家ID: {{ code }}", "unblock": "Player ID: {{ code }} was successfully unblocked", "block": "Player ID: {{ code }} was successfully blocked"}, "RG": {"TIMEOUT": {"title": "暂停", "text": "如果有玩家想要暂时停玩赌场，那么仅需要简单的设置，您就可以在一定时间内限制该玩家的访问权限。您只需选择玩家想要暂时停玩的赌场游戏和一个时长。", "notification_success": "玩家的暂停时长已成功修改。", "CASINO": {"text": "您需要选择暂停时长。在该时长内，玩家将无法进行任何赌场游戏。设置一旦完成，就无法撤销，但玩家仍然可能会收到我们所发送的与其相关的特别优惠。", "button_submit": "保存", "MODAL": {"confirmation_1": "玩家无法进行赌场游戏的时长为", "confirmation_2": "该限制的失效日期为XX。", "confirmation_3": "您确定吗？"}, "NOTICE": {"notice_1": "玩家暂停时长的结束日期与时间为"}, "LIMITS": {"Not set": "未设置", "1 day": "1天", "2 days": "2天", "1 week": "1周", "3 weeks": "3周", "6 weeks": "6周"}}}, "SELFEXCLUSION": {"title": "自愿禁赌", "text": "通过自愿禁赌选项来帮助玩家控制自身的博彩活动，可在所设定的时间内屏蔽玩家的账户。玩家在该时长内将无法游戏或存款，但仍可通过联系我们来取款。", "notification_success": "玩家的自愿禁赌时长已成功修改。", "CASINO": {"text": "请选择自愿禁赌时长。请注意：自愿禁赌期结束时，我们不会通知玩家。", "button_submit": "保存", "LIMITS": {"Not set": "未设置", "6 months": "6个月", "1 year": "1年", "3 years": "3年", "5 years": "5年"}, "MODAL": {"confirmation_1": "玩家将被禁玩赌场游戏长达", "confirmation_2": "您将无法移除该限制。您确定吗？"}, "NOTICE": {"notice_1": "玩家的自愿禁赌期将结束于"}}}, "REALITYCHECK": {"title": "游戏时长提醒", "text": "游戏时长提醒帮助玩家追踪记录自身的博彩活动，并能够提醒已持续游戏的时长。您可选择是否接收游戏时长提醒，还可设置提醒频率。", "notification_success": "游戏时长提醒设置已保存", "CASINO": {"text": "每隔多久显示游戏时长提醒", "button_submit": "保存游戏时长提醒", "REMINDS": {"off": "关闭", "20 min": "20分钟", "40 min": "40分钟", "1 hour": "1小时", "2 hours": "2小时", "3 hours": "3小时"}, "MODAL": {"confirmation_1": "现状核实将被设置为", "confirmation_2": "您确定吗？"}}}, "LOSSLIMIT": {"title": "损失限制", "text": "通过对玩家在赌场与体育板块中的投注设置损失限制，来管理玩家的博彩活动。", "notification_success": "玩家的损失限制已成功修改", "label": "金额:", "LIST": {"item_1": "每日限制于欧洲中部时间每日00:00更新。", "item_2": "每周限制于欧洲中部时间每周一00:00更新。", "item_3": "每月限制于欧洲中部时间每月1号的00:00更新。"}, "CASINO": {"text": "为赌场投注设置损失限制：", "button_submit": "保存", "LIMITS": {"no": "无", "daily": "每日", "weekly": "每周", "monthly": "每月"}, "MODAL": {"confirmation_1": "请注意：一旦设置了此限制，您将无法立即将其移除。当损失限制被调整至更为宽松的级别时，需先经过一个冷冻期，冷冻期过后才会生效。您是否确定要设置", "confirmation_2": "当损失限制被修改或移除时，需先经过一个冷冻期。玩家的此项限制将于{{ time }}后更改。", "confirmation_3": "您确定要进行此修改吗？"}, "NOTICE": {"notice_1": "玩家的损失限制被设置为", "notice_2": "，金额为", "notice_3": "至", "notice_4": "限制", "notice_5": "玩家之前的损失限制为", "notice_6": "限制将被修改"}}}, "DEPOSITLIMIT": {"title": "存款限置", "text": "存款限置决定了玩家在所选时间段内可存至其账户的金额大小。", "notification_success": "玩家的存款限制已成功修改", "label": "金额:", "LIST": {"item_1": "每日限制于欧洲中部时间每日00:00更新。", "item_2": "每周限制于欧洲中部时间每周一00:00更新。", "item_3": "每月限制于欧洲中部时间每月1号的00:00更新。"}, "CASINO": {"text": "为赌场投注设置存款限制：", "button_submit": "保存", "LIMITS": {"no": "无", "daily": "每日", "weekly": "每周", "monthly": "每月"}, "NOTICE": {"notice_1": "玩家的存款限制被设置为", "notice_2": "，金额为", "notice_3": "至", "notice_4": "限制", "notice_5": "玩家之前的存款限制为", "notice_6": "限制将被修改"}, "MODAL": {"confirmation_1": "请注意：一旦设置了此限制，您将无法立即将其移除。当损失限制被调整至更为宽松的级别时，需先经过一个冷冻期，冷冻期过后才会生效。您是否确定要设置", "confirmation_2": "当损失限制被修改或移除时，需先经过一个冷冻期。玩家的此项限制将于 24 hours 后更改。", "confirmation_3": "您确定要进行此修改吗？"}}}, "NOTICE": {"BUTTON": {"Change limit": "修改限制", "Cancel pending change": "取消待定的修改"}}, "MODAL": {"confirm": "是", "decline": "否"}, "pending": "7天"}, "PROMO": {"promotion": "推广", "bonusCoins": "奖励金币", "freeBets": "免费投注", "rebates": "退款", "virtualMoney": "虚拟金钱"}, "CSV": {"brandTitle": "Operator", "code": "玩家ID", "firstName": "First Name", "lastName": "Last Name", "currency": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "type": "Type", "createdAt": "Registration Date (GMT {{zone}})", "updatedAt": "Last Login (GMT {{zone}})", "country": "Country", "balance": "Balance"}}, "GAMEHISTORY": {"freeSpinsWon": "已赢得{{number}}次免费旋转", "freeSpinsRemaining": "剩余{{number}}次免费旋转", "TYPE": {"historyType": "历史类别", "gameHistoryLog": "游戏历史记录", "unfinishedBroken": "未完成与受损的"}, "GRID": {"account_type": "账户类别", "agentDomain": "代理商", "agentDomain_scope": "运营商/代理商", "balanceAfter": "转账后余额", "balanceBefore": "转账前余额", "bet": "投注金额", "bet_scope": "财务", "brandId": "运营商", "brandId_scope": "运营商/代理商", "contentProvider": "提供商", "currency": "货币", "currency_placeholder": "美元, 欧元······", "currency_scope": "财务", "customerCode": "客户代码", "device": "设备", "isFinished": "已结束", "unfinished": "未结束", "broken": "中断", "isTest": "测试", "gameCode": "游戏代码", "gameNameLabel": "游戏名称", "ggrPerc": "GGR %", "operator": "运营商", "outcome": "结果", "playerCode": "玩家ID", "playerCodeRequired": "玩家ID（必填项）", "debit": "Debit", "credit": "Credit", "revenue": "收入", "revenue_scope": "财务", "roundId": "局ID", "eventId": "Event ID", "extRoundId": "External Round ID", "status": "状态", "firstTs": "首局日期/时间", "ts": "末局日期/时间", "win": "奖金", "win_scope": "财务", "labelWin": "赢", "labelLose": "输", "labelTie": "平局", "labelVoid": "无效", "view": "查看", "internalRoundId": "内部游戏局ID", "insertedAt": "插入于", "testAccount": "测试", "realAccount": "真实", "resellerOperator": "转销商/运营商（必填项）", "transactionId": "交易ID ·", "gameProvider": "游戏提供商", "accountType": "账户类别", "onlyTest": "仅测试", "onlyReal": "仅真实", "onlyFinished": "仅已结束", "onlyUnfinished": "仅未结束", "onlyMobile": "仅移动终端", "onlyWeb": "仅网页", "dateTime": "日期/时间", "test": "测试", "real": "真实", "brandIdTitle": "品怕ID", "totalJpWin": "奖池奖金", "totalJpContribution": "奖池贡献", "requireLogout": "要求登出", "brokenIntegration": "出现故障的整合", "finalizing": "正在收尾", "tableName": "Table Name", "recoveryType__ne": "Recovery type is not", "recoveryType": "Recovery type", "force-finish": "Force-finish", "finalize": "Finalize", "null": "<PERSON><PERSON>"}, "GAME": {"freespin": "免费旋转", "freebet": "免费投注", "respin": "重转", "round": "Round", "spinId": "旋转ID", "operationId": "手術 ID", "spin": "旋转", "date": "日期", "currency": "货币", "freeSpinsRemaining": "免费旋转赢得{{value}}", "balanceBefore": "局前余额", "balanceAfter": "局后余额", "selectSymbol": "选择图案 #{{row.spinNumber}}", "rewardsSymbol": "图案", "rewardsExplode": "爆炸", "Bonus": "奖励 #{{row.spinNumber}}", "bonus": "奖励", "bonusResult": "奖励结果", "bet": "投注", "betAmount": "投注金额", "betLines": "投注/条线", "collectedOnReel": "收集在卷轴上", "win": "奖金", "stake": "赌注", "gameVersion": "游戏版本", "tableName": "Table name", "jackpot": "头奖", "jackpotId": "头奖 ID", "jackpotShot": "大奖射击", "jackpotReel": "困境卷轴", "jackpotWheel": "中奖轮", "jackpotStatus": "头奖状态", "jackpotInProgress": "积宝正在进行中", "jackpotReelType": "头奖转轴类型", "jackpotFinalScene": "头奖游戏末次旋转", "gameState": "游戏状态", "extraWin": "额外奖金", "extraMultiplier": "Extra multiplier", "extraSum": "额外总和", "pool": "池", "amount": "金额", "bonusSectionID": "奖励选择ID", "freeSpinsCount": "免费旋转计数", "freeSpinsCountOf": "免费旋转：数字（总数）", "freeSpinsWithStackedSymbol": "免费旋转与堆积的符号", "freeSpins": "免费旋转", "multiplier": "翻倍成数", "transactionID": "交易ID", "type": "类型", "endOfRound": "回合结束", "gameId": "游戏ID", "isPayment": "是否付款", "gameSymbolID": "图案ID: {{code}}", "rewardsHeadReward": "奖赏", "rewardsHeadMultiplier": "翻倍成数", "rewardsHeadWin": "奖金", "rewardsWay": "赔付组合", "rewardsLine": "赔付线", "rewardsScatter": "分散", "rewardsExtraScatter": "奖励额外分散", "rewardsInstantCash": "速赢现金", "maxWinReached": "已达到奖金上限", "coinRedeems": "硬币兑换", "bonusCounsRedeems": "奖励硬币兑换", "capped": "已达上限", "stand": "Stand", "maxWinReachedAmount": "Max win reached amount", "copyJsonToClipboard": "<PERSON><PERSON> JSON to clipboard", "downloadDetailedCSV": "Download detailed CSV", "notificationCopy": "Selected spin was copied to clipboard!", "notificationRoundCopy": "Selected round was copied to clipboard!", "notificationCopyFailed": "Copy to clipboard failed!", "notificationHistoryLoaded": "Spin history was loaded", "dateTime": "Date/Time", "paymentType": "支付类型", "sharedJackpotPrize": "Shared Jackpot prize", "finalization": "Finalization", "sharedPrizePayment": "分享奖金支付", "paymentTime": "支付时间", "tournamentWin": "锦标赛赢奖", "tournamentName": "锦标赛名称", "tournamentId": "锦标赛ID", "prizeDrop": "Prize Drop", "prizeWin": "赢得奖金", "featureName": "特色游戏名称", "winAmount": "Win Amount", "featureId": "特色游戏ID", "prize": "奖金", "operationType": "Operation type", "disconnect": "Disconnect", "CARD": {"rank": "牌阶", "split": "分牌", "cardDealer": "专家", "cardHand": "手牌", "insurance": "保险", "total": "总计", "play": "玩牌", "ante": "底注", "pairBonus": "攀对注奖金", "anteBonusWin": "底注奖金", "bet": "投注", "win": "奖金", "stake": "赌注", "playWin": "玩牌赢奖", "stakeWin": "底注赢奖", "doubleBet": "翻倍投注", "doubleWin": "翻倍赢奖", "insuranceBet": "保险投注", "insuranceWin": "保险赢奖", "pairWin": "攀对注赢奖", "pairBet": "攀对注投注"}, "freeSpinsStacked": "免费旋转堆叠", "freeSpinsSticky": "免费旋转黏性", "freeSpinsExpanding": "免费旋转扩展", "sw_sq": {"bonus": "奖励", "gem": "钻石", "result": "结果", "selectedGem": "所选钻石"}, "sw_tcb": {"request_fold": "弃牌", "HAND_COMBINATION_FLUSH": "金花", "HAND_COMBINATION_HIGH_CARD": " 散牌", "HAND_COMBINATION_NOT_QUALIFY": "庄家不够格", "HAND_COMBINATION_PAIR": "对子", "HAND_COMBINATION_PAIR_10": "一对10", "HAND_COMBINATION_PAIR_2": "一对2", "HAND_COMBINATION_PAIR_3": "一对3", "HAND_COMBINATION_PAIR_4": "一对4", "HAND_COMBINATION_PAIR_5": "一对5", "HAND_COMBINATION_PAIR_6": "一对6", "HAND_COMBINATION_PAIR_7": "一对7", "HAND_COMBINATION_PAIR_8": "一对8", "HAND_COMBINATION_PAIR_9": "一对9", "HAND_COMBINATION_PAIR_A": "一对A", "HAND_COMBINATION_PAIR_J": "一对J", "HAND_COMBINATION_PAIR_K": "一对K", "HAND_COMBINATION_PAIR_Q": "一对Q", "HAND_COMBINATION_PRIAL": "豹子", "HAND_COMBINATION_RANK_10": "最大牌为10", "HAND_COMBINATION_RANK_2": "最大牌为2", "HAND_COMBINATION_RANK_3": "最大牌为3", "HAND_COMBINATION_RANK_4": "最大牌为4", "HAND_COMBINATION_RANK_5": "最大牌为5", "HAND_COMBINATION_RANK_6": "最大牌为6", "HAND_COMBINATION_RANK_7": "最大牌为7", "HAND_COMBINATION_RANK_8": "最大牌为8", "HAND_COMBINATION_RANK_9": "最大牌为9", "HAND_COMBINATION_RANK_A": "最大牌为A", "HAND_COMBINATION_RANK_J": "最大牌为J", "HAND_COMBINATION_RANK_K": "最大牌为K", "HAND_COMBINATION_RANK_Q": "最大牌为Q", "HAND_COMBINATION_RUN": "顺子", "HAND_COMBINATION_RUNNING_FLUSH": "同花顺", "HAND_COMBINATION_RUNNING_FLUSH_A23": "同花顺A-2-3", "HAND_COMBINATION_RUN_A23": "顺子A-2-3"}, "sw_er": {"positions": "Positions", "stopPosition": "停止", "payout": "赔率", "StraightPayout": "直接下注", "SplitPayout": "分注", "CornerPayout": "角注", "LinePayout": "线注", "ColumnDozenPayout": "下注一打数位", "ColorPayoutDefinition": "颜色", "HighLowPayout": "小（1-18）/ 大（19-36）", "OddPayoutDefinition": "奇数", "EvenPayoutDefinition": "偶数", "colorRED": "红色", "colorBLACK": "黑色"}, "sw_bjc": {"request_stand": "Stand"}, "POOL": {"mega": "超巨奖", "big": "至尊", "medium": "超大", "small": "迷你", "major": "巨级奖", "grand": "至尊", "minor": "高级奖", "mini": "初级奖", "A": "巨级奖", "B": "重大的", "C": "次要", "D": "微型", "super": "超巨奖", "main": "神龙宝石头奖", "fruit-vegas": "硕果赌城", "monkey-pays": "猴子赏福", "x2": "X2", "x3": "X3", "x5": "X5", "x10": "X10", "X2": "X2", "X3": "X3", "X5": "X5", "X10": "X10", "level1": "巨级奖", "level2": "巨级奖", "level3": "巨级奖", "level4": "巨级奖", "level5": "巨级奖", "level6": "巨级奖", "level7": "巨级奖", "sw_omqjp": {"A": "老夫子", "B": "大番薯", "C": "秦先生"}, "sw_wfww": {"main": "五福娃娃头奖"}, "sw_lfs": {"maya-jp": "六福兽头奖"}, "sw_dosc7s": {"two-powerful-dragons": "双散7"}, "sw_ar": {"mini": "初级奖", "minor": "高级奖", "sw-fire-reel-major": "巨级奖", "sw-reel-mega": "超巨奖"}, "sw_azreeu": {"mini": "初级奖", "minor": "高级奖", "sw-aztec-reel-major-eu": "巨级奖", "sw-reel-mega-eu": "超巨奖"}, "sw_azresl": {"mini": "初级奖", "minor": "高级奖", "sw-aztec-reel-major-eu": "巨级奖", "sw-reel-mega-eu": "超巨奖"}, "sw_fr": {"mini": "初级奖", "minor": "高级奖", "sw-fire-reel-major": "巨级奖", "sw-reel-mega": "超巨奖"}, "sw_frreeu": {"mini": "初级奖", "minor": "高级奖", "sw-aztec-reel-major-eu": "巨级奖", "sw-reel-mega-eu": "超巨奖"}, "sw_frresl": {"mini": "初级奖", "minor": "高级奖", "sw-aztec-reel-major-eu": "巨级奖", "sw-reel-mega-eu": "超巨奖"}, "sw_glreeu": {"mini": "初级奖", "minor": "高级奖", "sw-gladiator-reel-major-eu": "巨级奖", "sw-reel-mega-eu": "超巨奖"}, "sw_glre_slp": {"mini": "初级奖", "minor": "高级奖", "sw-gladiator-reel-major-eu": "巨级奖", "sw-reel-mega-eu": "超巨奖"}, "sw_mr": {"mini": "初级奖", "minor": "高级奖", "sw-metal-reel-major": "巨级奖", "sw-reel-mega": "超巨奖"}, "sw_noreeu": {"mini": "初级奖", "minor": "高级奖", "sw-river-boat-major-eu": "巨级奖", "sw-reel-mega-eu": "超巨奖"}, "sw_noresl": {"mini": "初级奖", "minor": "高级奖", "sw-river-boat-major-eu": "巨级奖", "sw-reel-mega-eu": "超巨奖"}, "sw_poreeu": {"mini": "初级奖", "minor": "高级奖", "sw-gladiator-reel-major-eu": "巨级奖", "sw-reel-mega-eu": "超巨奖"}, "sw_poresl": {"mini": "初级奖", "minor": "高级奖", "sw-gladiator-reel-major-eu": "巨级奖", "sw-reel-mega-eu": "超巨奖"}, "sw_rr": {"mini": "初级奖", "minor": "高级奖", "sw-water-reel-major": "巨级奖", "sw-reel-mega": "超巨奖"}, "sw_rireeu": {"mini": "初级奖", "minor": "高级奖", "sw-river-boat-major-eu": "巨级奖", "sw-reel-mega-eu": "超巨奖"}, "sw_riresl": {"mini": "初级奖", "minor": "高级奖", "sw-river-boat-major-eu": "巨级奖", "sw-reel-mega-eu": "超巨奖"}, "sw_bloo": {"blood_sport_major": "Major <PERSON><PERSON>", "blood_sport_grand": "Grand Jackpot"}, "sw_bl_slp": {"blood_sport_major": "Major <PERSON><PERSON>", "blood_sport_grand": "Grand Jackpot"}, "sw_wrl": {"mini": "初级奖", "minor": "高级奖", "sw-water-reel-major": "巨级奖", "sw-reel-mega": "超巨奖"}, "sw_desheurb": {"minor": "初级奖", "major": "高级奖", "grand-genie-shot-eu": "巨级奖", "super-shot-eu": "超巨奖"}, "sw_ges": {"minor": "初级奖", "major": "高级奖", "grand-genie-shot": "巨级奖", "super-shot": "超巨奖"}, "sw_ges_eu": {"minor": "初级奖", "major": "高级奖", "grand-genie-shot-eu": "巨级奖", "super-shot-eu": "超巨奖"}, "sw_gs": {"minor": "初级奖", "major": "高级奖", "grand-gold-shot": "巨级奖", "super-shot": "超巨奖"}, "sw_gs_eu": {"minor": "初级奖", "major": "高级奖", "grand-gold-shot-eu": "巨级奖", "super-shot-eu": "超巨奖"}, "sw_hosheu": {"minor": "初级奖", "major": "高级奖", "grand-pot-shot-eu": "巨级奖", "super-shot-eu": "超巨奖"}, "sw_mdls": {"minor": "初级奖", "major": "高级奖", "grand-gold-shot": "巨级奖", "super-shot": "超巨奖"}, "sw_ps": {"minor": "初级奖", "major": "高级奖", "grand-pot-shot": "巨级奖", "super-shot": "超巨奖"}, "sw_ps_eu": {"minor": "初级奖", "major": "高级奖", "grand-pot-shot-eu": "巨级奖", "super-shot-eu": "超巨奖"}, "sw_tisheu": {"minor": "初级奖", "major": "高级奖", "grand-gold-shot-eu": "巨级奖", "super-shot-eu": "超巨奖"}, "sw_ws": {"minor": "初级奖", "major": "高级奖", "grand-pot-shot": "巨级奖", "super-shot": "超巨奖"}, "sw_suli": {"main": "累积头奖"}, "sw_suli_chf": {"main": "累积头奖"}, "sw_sulisped": {"main": "累积头奖"}, "sw_suel": {"main": "累积头奖"}, "sw_ges2": {"grand-genie-shot": "巨级奖"}, "sw_ff": {"main": "烟火节头奖"}, "sw_doab": {"downton_abbey_crawley": "<PERSON><PERSON><PERSON>", "downton_abbey_grantham": "<PERSON><PERSON>", "downton_abbey_village": "Village"}, "sw_doab_slp": {"downton_abbey_crawley_slp": "<PERSON><PERSON><PERSON>", "downton_abbey_grantham_slp": "<PERSON><PERSON>", "downton_abbey_village_slp": "Village"}, "sw_kkit": {"karate_kid_italy": "威小子·意大利"}, "sw_thmase": {"magnificent_seven": "豪勇累积大奖"}, "sw_thmase_slp": {"magnificent_seven": "豪勇累积大奖"}, "sw_kk": {"karate-kid": "威小子·意"}, "sw_kaki_slp": {"karate-kid": "威小子·意"}, "sw_taoftwdrjaed": {"gold": "红色", "standard": "蓝色"}}, "SRT": {"challengeWinPool": "挑战赢奖", "tournamentWinPool": {"pool0": "锦标赛奖金 - 投注范围 1", "pool1": "锦标赛奖金 - 投注范围 2", "pool2": "锦标赛奖金 - 投注范围 3", "pool3": "锦标赛奖金 - 投注范围 4"}, "tournament": "锦标赛奖金", "challenge": "挑战", "playerCoins": "玩家代幣", "coins": "代幣", "collectedBadges": "已收集勛章", "redeemAmount": "兌換金額", "vipLevel": "VIP等級", "points": "點數", "winAmount": "獎金金額", "xpAmount": "XP金額", "xp": "XP"}}, "EXTERNAL": {"messageForceFinish": "You are about to force-finish the round. The round will be closed as it is and all remaining bonus features will not be played out. Additionally, the bet and wins will not be reverted. Are you sure?"}, "INTERNAL": {"ignoreMerchantParams": "You are going to force finish round ignoring the supportForceFinishAndRevert flag of the merchant and close round in SW wallet. Are you sure?", "messageForceFinishBroken": "您即将强制结束中断了的游戏局。请注意：这不会使投注和奖金恢复；这有可能造成支付损失。该操作一旦执行，就无法撤销。您确定要继续吗？", "messageForceFinish": "您即将强制结束本游戏局。请注意：这不会使投注和奖金恢复。该操作一旦执行，就无法撤销。您确定要继续吗？ ", "messageRevertBroken": "您即将关闭中断了的游戏局并使投注和奖金恢复。请注意：这有可能造成支付损失。您确定要恢复吗（该操作一旦执行，就无法撤销）？", "messageRevert": "您即将关闭本游戏局并使投注和奖金恢复。该操作一旦执行，就无法撤销。您确定要恢复吗？", "messageRetry": "您确定要重试待定吗？", "messageFinalize": "Do you really want to finalize the round?", "messageManualFinalize": "Do you really want to finalize the round manually?", "messageTransferOut": "Do you really want to transfer out for round?", "yesProceed": "是的，继续", "yesRevert": "是的，恢复", "yesRetry": "是的，重试", "notify": "请给出至少一个以下参数，来继续执行搜索：玩家ID，局ID，仅未结束", "notifyUnfinished": "请输入“玩家ID”来继续搜索", "notifyNote": "提示：“转销商/运营商”为必填项", "forceFinish": "强制结束", "revert": "恢复", "retryPending": "重试待定", "requireTransferOut": "Transfer out", "finalize": "Finalize", "NOTIFICATIONS": {"forceFinished": "Round has been finished successfully", "roundFinished": "本局游戏已成功结束，所有事件均已完成", "roundClosed": "本局游戏已成功关闭", "roundFinalized": "Round has been finalized successfully", "betsReverted": "投注和奖金已恢复", "noForceFinish": "无需强制结束", "noRevert": "无需恢复", "noRetry": "无需重试待定", "retrySuccess": "已成功重试待定", "note": "请注意：表格中的局状态将会在2分钟后更新"}}, "CSV": {"playerCode": "玩家ID", "roundId": "Round ID", "gameName": "Game Name", "gameCode": "Game Code", "finished": "Finished", "device": "<PERSON><PERSON>", "type": "Account type", "firstTs": "First Date/Time (GMT {{zone}})", "ts": "Last Date/Time (GMT {{zone}})", "currency": "<PERSON><PERSON><PERSON><PERSON>", "bet": "Bet Amount", "win": "Win Amount", "revenue": "Revenue", "outcome": "Outcome", "ggr": "GGR %", "totalJpWin": "Jp Win", "actions": "actions", "extTrxId": "Transaction ID", "gameProviderCode": "Game Provider", "insertedAt": "Date/Time (GMT {{zone}})", "balanceBefore": "Balance Before", "balanceAfter": "Balance After"}}, "REPORT_PLAYERS": {"note": "Please, select an Operator to proceed with search", "filterNote": "Please specify Date within one month period. You can see only history values for the past 3 months", "GRID": {"paymentDate": "日期", "playerCode": "玩家ID", "gameCode": "游戏名称", "currency": "货币", "currency_placeholder": "美元, 欧元······", "playedGames": "已玩游戏", "totalBets": "总赌金", "totalFreebetWins": "免费投注总奖金", "totalJpWins": "头奖总奖金", "totalWins": "总奖金", "GGR": "总GGR", "rtpPerc": "RTP%", "debits": "出账", "credits": "入账"}, "CSV": {"playerCode": "玩家ID", "debits": "Debits", "credits": "Credits", "currency": "<PERSON><PERSON><PERSON><PERSON>", "playedGames": "Played Games", "totalBets": "Total Stakes", "totalWins": "Total Returns", "totalJpWins": "Jackpots Total Win", "GGR": "Total GGR", "RTP": "RTP %"}}, "REPORT_CURRENCY": {"note": "Please, select an Operator to proceed with search", "filterNote": "Please specify Date within one month period. You can see only history values for the past 3 months", "GRID": {"bets": "总赌注", "betsUsd": "总赌注(美元)", "currency": "货币", "currency_placeholder": "美元, 欧元······", "date": "日期", "freebetWins": "免费投注奖金", "freebetWinsUsd": "免费投注奖金（美元)", "GGR": "总GGR", "ggrUsd": "总GGR (美元)", "jpWins": "头奖奖金", "jpWinsUsd": "头奖奖金（美元)", "playedGames": "已玩游戏", "totalJpWins": "头奖总奖金", "winnings": "总收益", "winningsUsd": "总收益(美元)"}}, "REPORT_FINANCIAL": {"GRID": {"currency": "货币", "currency_placeholder": "美元，欧元……", "date": "日期", "to": "至", "from": "从", "initiatorName": "发起人名称"}, "FILTER": {"type": "交易类别", "path": "转销商/运营商（必选项）", "credit": "入账", "debit": "出账"}}, "REPORT_RG": {"GRID": {"player_code": "玩家ID", "suspension_type": "停玩类别", "suspension_type_timeout": "暂停", "suspension_type_exclusion": "自愿禁赌", "product_type": "产品类别", "product_type_casino": "游乐场", "product_type_sports": "体育", "product_type_undefined": "未定义类别", "created_at": "开始时间", "end_time": "结束时间"}}, "PAYMENTS_TRANSFERS": {"deposits_list": "取款列表", "withdrawals_list": "存款列表", "changeStatus": "您确定要改变 {{statusTitle}} 的状态吗", "note": "Please, select an Operator to proceed with search", "GRID": {"path": "转销商/运营商（必填项）", "cpTransferId": "CP转账ID", "trxId": "转账ID", "extTrxId": "外部交易ID", "playerCode": "玩家ID", "transferSource": "从", "transferDest": "至", "amount": "金额", "currency": "货币", "playerBalanceAfter": "转账后余额", "currencyCode": "货币", "remainingBalance": "所剩余额", "updatedBalance": "更新后的余额", "startDate": "开始日期/时间", "endDate": "结束日期/时间", "orderStatus": "状态", "processedBy": "处理者", "paymentMethodCode": "PSP", "isOnline": "在线", "orderId": "支付ID ", "approved": "已批准", "declined": "已拒绝", "blocked": "已屏蔽", "init": "初始化"}, "CSV": {"trxId": "Transfer ID", "playerCode": "玩家ID", "from": "From", "extTrxId": "extTrxId", "amount": "Amount", "playerBalanceAfter": "Balance After", "currencyCode": "<PERSON><PERSON><PERSON><PERSON>", "to": "To", "startDate": "Start Date / Time (GMT {{zone}})", "endDate": "End Date / Time (GMT {{zone}})", "orderStatus": "Status"}}, "GGR (EUR)": "", "HEADER": {"Home": "主页", "Logout": "登出", "subTitle": "FALCON", "suffix": "专业版", "title": "SKYWIND"}, "LANGUAGE": {"Chinese": "中文", "English": "English", "Japanese": "日本語"}, "LANGUAGE_CODE": {"Chinese": "中文 (ZH)", "English": "English (EN)", "Japanese": "日本語 (JA)", "ChineseTraditional": "繁體中文 (ZH-TW)", "Malay": "Bahasa Malaysia (MS)", "Korean": "한국어 (KO)", "Thai": "Thai (TH)", "Vietnamese": "<PERSON><PERSON><PERSON><PERSON> (VI)", "Indonesian": "Bahasa Indonesia (ID)", "Romanian": "Romanian (RO)", "Italian": "<PERSON><PERSON> (IT)", "Greek": "Ελληνικά (EL)"}, "TWOFA": {"title": "双重认证", "subTitle_confirmation": "需要确认", "subTitle_pleaseSelect": "请在以下双重认证方式中选择一个", "subTitle_pleaseSelectChange": "请在以下双重认证方式中选择一个", "subTitle_setup": "正在设置双重认证", "sms_pleaseEnterPhoneNumber": "请输入您的手机号码", "sms_pleaseEnterCode": "请输入已发送至您的手机号{{ number }}的验证码", "sms_invalidNumber": "您输入的手机号码无效，请仅输入数字和加号", "email_pleaseEnterCode": "请输入已发送至您的电子邮箱{{ email }}的验证码", "googleAuth_pleaseEnterCode": "请输入Google身份验证器为您的登录邮箱{{ email }}生成的验证码", "googleAuth_scanThisQRCode": "请使用Google身份验证器应用程序扫描二维码", "googleAuth_showStringSecretKey": "将密钥转换为字符串", "googleAuth_useStringSecretKey": "……或直接输入密钥", "setupConfirm_postfix": "以确保完成设置", "phoneNumber": "手机号码", "code": "验证码", "submit": "登录", "next": "下一步", "send": "发送", "youCanChangeType": "您可@更改@已选认证方式", "twofa_googleAuth": "Google身份验证器", "twofa_phoneBySMS": "手机（短信）", "twofa_email": "电子邮箱", "validationError_codeInvalid": "您输入的验证码无效，请仅输入数字", "error_code716": "您输入的验证码无效，请再试一次（错误代码716）", "error_code717": "验证会话已过期，请再试一次（错误代码717）", "error_code718": "无法验证，请再试一次（错误代码718）", "error_code719": "无法发送验证短信，请稍后再试（错误代码719）", "error_code720": "无法发送验证邮件，请稍后再试（错误代码720）", "error_code721": "无法使用您所选择的双重认证方式进行验证。请联系客服（错误代码721）", "error_code722": "无法验证，请稍后再试或联系客服（错误代码722）", "error_code723": "无法验证，请稍后再试或联系客服（错误代码723）"}, "PASSWORD_CHANGE": {"passwordCurrent": "现有密码", "passwordNew": "新密码", "passwordConfirm": "确认新密码", "passwordRequirements": "您的新密码需", "passwordNotMatch": "您的新密码和确认密码不一致", "passwordMinLength": "包含至少8个字符", "passwordContainDigit": "包含至少{{value}}个数字", "passwordContainLowercase": "包含至少{{value}}个小写英文字母", "passwordContainUppercase": "包含至少{{value}}个大写英文字母", "passwordChange": "更改密码", "passwordChangedSuccessfully": "Password was changed successfully"}, "ADD_GAMES_CASCADE": {"title": "添加游戏级联 {{name}}", "hintText": "您可通过此界面来为商业结构中的已选部分同时添加多个游戏。\n您所添加的游戏将应用至已选实体和其子实体。", "selectedEntity": "已选实体：", "availableGames": "可选游戏：", "noGamesSelected": "无已选游戏", "btnAddNGames": "添加{{ n }}个游戏", "gamesAddedSuccessfully": "{{ count }} games were added successfully!", "btnSave": "保存"}, "ENTITY_SETUP": {"REGIONAL": {"MODALS": {"path": "转销商/运营商", "entityPath": "Business entity path", "selectTransactionDirection": "选择交易方向", "placeholderSearch": "搜索", "btnCancel": "取消", "btnApply": "应用", "btnAdd": "添加", "addNewCountry": "添加新国家", "chooseCountry": "选择国家", "selectJurisdiction": "选择辖区", "noJurisdiction": "无该辖区", "manageBalance": "管理余额", "manageLanguages": "管理语言", "manageJurisdictions": "管理辖区", "manageCurrency": "Manage Currency", "manageCountry": "Manage Country", "selectedCurrency": "所选货币", "chooseCurrency": "选择货币", "addCurrency": "添加货币", "balance": "余额", "yourBalance": "Your Balance", "parentBalance": "Parent Balance", "transfer": "转款", "available": "可用", "availableToDeposit": "Available amount to deposit", "availableToWithdraw": "Available amount to withdraw", "deposit": "存款", "withdrawal": "提款", "creditFrom": "Credit from", "debitTo": "Debit to", "optional": "（可选）", "selected": "已选", "totalBalance": "您的总余额将为", "error_noCountries": "无国家可选", "error_noCurrencies": "无货币可选", "error_balanceMin": "Please, put a positive value", "error_balanceNotEquals": "Please, put a positive value", "notificationCountryAdded": "已添加国家\"{{name}}\" ", "notificationCountriesAdded": "Countries list has been updated", "notificationCountryRemoved": "Country was removed", "notificationCurrenciesAdded": "Currencies list has been updated", "notificationUserAdded": "已创建用户\"{{name}}\" ", "notificationUserUpdated": "已修改用户\"{{name}}\" ", "notificationCurrencyBalanceAdded": "余额为{{amount}} {{currency}}的货币{{currency}}已添加至实体{{entity}}", "notificationCurrencyAdded": "货币{{currency}}已添加实体{{entity}}", "notificationBalanceSubtracted": "{{amount}} {{currency}} 已从{{entity}}中减除", "notificationBalanceAdded": "{{amount}} {{currency}}已添加{{entity}}", "jurisdictionsErrorLabel": "At least one jurisdiction should be selected"}, "tabname": "区域", "useJurisdictionsDataOnly": "Use allowed/restricted/default country of the jurisdiction", "titleAllowedCountries": "Allowed Countries", "titleRestrictedCountries": "Restricted Countries", "titleBlockedCountries": "被阻止的国家", "titleCountries": "国家", "titleCurrencies": "货币", "titleLanguages": "语言", "titleJurisdictions": "辖区", "titleDeploymentGroups": "Deployment groups", "add": "添加", "manage": "管理", "countryName": "名称（代码）", "jurisdiction": "辖区", "selectAll": "选择所有游戏", "unSelectAll": "Unselect All", "currencyName": "名称（代码）", "balance": "余额", "languageName": "名称（代码）", "jurisdictionName": "名称（代码）", "default": "默认", "setDefault": "默认设置", "detachDefaultGroup": "Detach Group", "actionDelete": "删除", "btnConfirmDelete": "确认删除", "btnClose": "关系", "pleaseConfirm": "请确认您的操作", "languagesWereAdded": "Language list was updated successfully!", "regionalsWereAdded": "Regional items list was updated successfully!", "currencyDeleted": "Currency was successfully removed", "deploymentGroupDescription": "Description", "deploymentGroupRoute": "Route"}, "USERS": {"MODALS": {"createUser": "创建用户", "editUser": "编辑用户", "userCreated": "成功建立用户", "userModified": "成功修改用户", "secretKey": "密钥", "btnClose": "关闭", "btnSave": "保存修改", "confirmationRequired": "需要确认", "unblockUserModalTitle": "取消屏蔽用户", "btnUnblock": "取消屏蔽", "userLockedLoginTillDate": "用户\"{{username}}\"已被锁定", "userLockedChangePasswordTillDate": "密码更改已被锁定，直到", "pleaseConfirm": "请确认您的操作", "pleaseSelectAndConfirm2FAReset": "请选择需要重置的认证类型并确认提交", "btnConfirmReset": "确认重置", "btnConfirmDeleteUser": "确认删除", "twofaUserResetNotification": "您试图对用户{{username}}重置双重认证设置", "deleteUserNotification": "您将要删除用户账户\"{{username}}\"", "firstname": "名", "lastname": "姓", "username": "用户名", "password": "密码", "email": "电子邮件", "phoneNumber": "手机号码", "status": "状态", "userType": "Account Type", "roles": "角色", "additionalroles": "额外的角色"}, "tabname": "用户", "entity": "Entity", "name": "姓名", "username": "用户名", "role": "角色", "email": "电子邮件", "created": "已创建", "modified": "已修改", "status": "状态", "statusActive": "已激活", "statusInactive": "未激活", "locked_by_auth": "Locked", "userTypeBO": "Back-office access", "userTypeOperatorAPI": "API access", "userOperatorNoType": "No Type", "editUserAction": "编辑用户", "deleteUserAction": "删除用户", "reset2faAction": "重置双重认证设置", "btnCreateUser": "创建用户", "searchPlaceholder": "搜索", "twofaResetSuccessfull": "已为用户\"{{ username }}\"清除双重认证设置", "userRemoved": "已移除用户\"{{ username }}\" ", "userUnblocked": "已屏蔽用户\"{{ username }}\" ", "boUserPasswordExpires": "Password expires every:", "apiUserPasswordExpires": "Password never expires.", "changePasswordNote": "重要信息：您正在尝试更改API用户的密码！请不要忘记在集成时对其进行更改。如有任何疑问，请联系SKYWIND客户支持部门。", "changeUserTypeNote": "重要信息：您正在尝试将用户类型从API访问更改为后台访问！此操作可能会导致集成问题。如有任何疑问，请联系SKYWIND客户支持部门。", "PERIOD_TYPE": {"minutely": "minutely", "hourly": "hourly", "daily": "daily", "weekly": "weekly", "monthly": "monthly", "yearly": "yearly"}}, "NOTIFICATIONS": {"tabname": "通知", "changesUnsavedNotifications": "Do you really want to leave this page? All unsaved changes will be lost.", "savedSuccess": "已成功保存通知", "addNew": "添加新通知", "save": "保存", "removeItem": "移除条目", "emptyMessage": "未设置任何条目", "emailFrequency": "Email frequency", "sendEmailsFrom": "Send emails from", "sendEmailsTo": "to", "notificationDateError": "One of the fields must be filled", "LOWBALANCE": {"tabTitle": "Low Balance Notifications", "EMAIL": {"title": "低余额邮件通知", "placeholder": "请插入邮件", "description": "玩家余额较低时，系统会通过其提供的联系方式发送有关低余额的通知"}, "PHONE": {"title": "低余额手机通知", "placeholder": "请插入手机号码", "description": "玩家余额较低时，系统会通过其提供的联系方式发送有关低余额的通知"}}, "PROMO": {"title": "Promotion notifications", "placeholder": "Please insert email", "description": "Receive promotion notifications for these email addresses"}, "ENGAGEMENT": {"tabTitle": "Engagement Tools Notifications", "title": "Engagement Tools Notifications", "placeholder": "Please insert email", "description": "Send Engagement Tools reports to these email addresses:"}}, "GAMES": {"MODALS": {"editGames": "编辑游戏", "btnCancel": "取消", "btnPrevious": "前一个", "btnNext": "后一个", "btnApply": "应用", "btnFinish": "完成", "selectGames": "选择游戏", "newGamesSetup": "新游戏设置", "preview": "预览", "selectAll": "选择所有游戏", "gameName": "游戏名称", "gameReady": "游戏已经可以保存了", "ready": "准备就绪", "noLabels": "无标签", "setRoyalties": "设置Royalties", "selectStatus": "选择状态", "titleConfirmation": "需要确认", "messageFailedSingle": "未能成功移除已被添加至相应子实体的实体游戏<strong>{{title}}</strong> (code: {{code}}) ", "messageConfirmRemove": "请确认强制移除", "messageFailedMultiple": "未能成功移除已被添加至相应子实体的实体游戏", "cancelRemove": "取消移除", "confirmForceRemove": "确认强制移除", "aamsCode": "AAMS Code", "mustWinJackpotBundled": "MWJP Bundled", "externalGameId": "External Game ID", "specificGamesAlert": "Please note that the current entity has game limits filter which applies to all games, however the following games are \"special games\" and the filter will not apply to them:", "kill_sessions": "Are you sure? All active sessions of this game will be killed"}, "MANAGE_GRID": {"providerTitle": "游戏提供者", "title": "游戏名称", "code": "游戏代码", "isFreebetSupported": "Freebet Supported", "isBonusCoinsSupported": "Bonus Coins Supported", "isMarketplaceSupported": "Marketplace Supported", "isCustomLimitsSupported": "Сustom Limits Supported", "limitFiltersSupported": "Limit Filters Supported", "aamsCode": "AAMS Code", "mustWinJackpotBundled": "MWJP Bundled"}, "SET_JACKPOT": {"title": "Set Jackpot", "create": "Create", "type": "Jackpot type", "set-notification": "Jackpot was set for {{ gameCode }} game"}, "tabname": "游戏", "generalGameInfoTabName": "General Games Info", "jpGameInfoTabName": "JP Games Info", "createGame": "Create Game", "cloneGame": "Clone Game", "editGame": "Edit Game", "setJp": "Set Jackpot", "clone": "<PERSON><PERSON>", "add": "Add", "exportConfig": "Export Config", "btnManageGames": "管理游戏", "searchPlaceholder": "按名称或代码搜索……", "title": "名称", "providerTitle": "提供者", "code": "代码", "royalties": "使用费", "labels": "标签", "status": "状态", "type": "Type", "special": "Special game", "connectedPool": "Connected Pool/JackpotId", "notificationSingleGameRemoved": "{{amount}} game was removed", "notificationMultipleGamesRemoved": "{{amount}} games were removed", "notificationSingleGameAdded": "{{amount}} game was added", "notificationMultipleGamesAdded": "{{amount}} games were added", "notificationGameCreated": "\"{{game}}\" game was created successfully", "notificationGameUpdated": "\"{{game}}\" game was updated successfully", "notificationGameAddedToEntity": "\"{{game}}\" game was added to entity successfully", "gameCode": "Game code", "gameTitle": "Game title", "url": "Url", "addGameToEntity": "Add game to entity", "entityPath": "Business entity path", "CSV": {"gameCode": "Game code", "billingConfigurationLevel": "Billing Configuration Level", "businessEntityConfigurationLevel": "Business Entity Configuration Level", "isInherited": "Is Inherited", "configuredOn": "Configured On", "jpId": "JP <PERSON>", "jpType": "JP Type", "gameName": "Game name", "jpGame": "JP game", "connectedPool": "Connected Pool/JackpotId", "status": "Status"}}, "WHITELISTING": {"MODALS": {"editSite": "编辑可用网站", "addSite": "添加可用网站", "siteUrl": "网站URL", "default": "<PERSON><PERSON><PERSON>", "siteTitle": "网站标题", "operatorSiteGroupName": "Operator Site Group Name", "externalCode": "External Code", "isDefault": "<PERSON><PERSON><PERSON>", "status": "状态", "btnClose": "关闭", "btnSave": "保存修改", "ok": "好的", "statusLog": "状态日志", "confirmSetAsDefault": "Are you sure you want to set \"{{url}}\" site as default?", "successSetAsDefault": "Site \"{{url}}\" was successfully set as default"}, "tabname": "制作白名单", "boIpWhitelist": "Game launch IP Whitelist", "userIpWhitelist": "BO & API IP Whitelist", "btnRemoveSelected": "移除已选项", "ipAddress": "IP地址", "actions": "操作", "whitelistNotCreated": "为创建白名单", "noItemsFound": "未找到数据项", "placeholderEnterIP": "输入IP地址", "actionRemove": "移除条目", "actionEdit": "编辑条目", "btnAdd": "添加", "availableSites": "可用网站", "btnAddSite": "添加网站", "siteStatusActive": "已激活", "siteStatusInactive": "未激活", "siteStatusInfo": "Info", "gridSiteTitle": "姓名", "entity": "Entity", "gridSiteUrl": "网站URL", "gridSiteStatus": "状态", "operatorSiteGroupName": "Operator Site Group Name", "externalCode": "External Code", "title": "使用网站验证", "notificationRemoved": "已成功移除该网站", "notificationSaved": "已成功保存该网站", "notificationAdded": "Site was successfully added", "notificationChanged": "Status was successfully changed", "notificationSelectedRemoved": "已成功移除所选网站", "confirmationRemoved": "您确定要删除该网站吗？", "confirmationSelectedRemoved": "您确定要删除所选网站吗？", "checkWebsiteWhitelisted": "Check WebSite Whitelisted", "activate": "激活", "deactivate": "关闭", "remove": "移除条目"}, "PLAYER_BLOCKLIST": {"tabname": "玩家屏蔽列表", "playerBlocklist": "玩家屏蔽列表", "btnRestorePlayers": "恢复所选玩家", "btnSuspendPlayer": "冻结玩家", "placeholderEnterCode": "输入玩家代码", "playerCode": "玩家代码", "actions": "操作", "actionRestore": "恢复"}, "TWOFA_SETUP": {"tabname": "安全", "twofaStatusPrefix": "双重认证", "twofaStatusActive": "已激活", "twofaStatusInactive": "未激活", "btnActivate": "激活", "btnDeactivate": "关闭", "typeStateEnabled": "启用", "typeStateDisabled": "禁用", "twofaTypeSMS": "短信", "twofaTypeEMail": "电子邮件", "twofaTypeGoogleAuth": "Google身份验证器", "btnSave": "保存修改", "emailTemplateFrom": "发件人", "emailTemplateSubject": "主题", "emailTemplateBody": "HTML模板", "smsTemplateBody": "文本", "useDefault": "使用默认模板", "exampleTemplate": "模板示例", "availableVariables": "可得变量", "NOTIFICATIONS": {"activated": "已成功激活双重认证", "deactivated": "已成功禁用双重认证", "saved": "已成功保存双重认证设置", "inactive": "双重认证处于未激活状态，因为所有的认证方式都已禁用"}}, "DOMAINS": {"maintenanceUrl": "维护URL", "maintenancePlaceholder": "URL格式：https://example.com", "maintenanceUrlEditSuccess": "维护URL已成功编辑", "maintenanceUrlConfirmation": "您确定要保存维护URL吗？", "entityDomains": "实体域名", "btnApply": "应用", "tabname": "域名", "domains": "域名", "domain": "域名", "static": {"static": "静态域名", "lobby": "大厅域名", "ehub": "E-Hub域名", "tags": "静态标签"}, "dynamic": "动态域名", "notSet": "未设置", "set": "已设置", "reset": "重置", "environment": "环境", "btnYes": "是", "btnNo": "否", "formTitle": "通过域名来验证（每行只可填一个）", "textareaPlaceholder": "域名", "btnCancel": "取消", "selectDomain": "选择域", "modalTitle": "为实体选择{{ type }}域", "saved": "已成功保存实体域名！", "dynamicDomainsActivated": "动态域路由已激活", "dynamicDomainsDeactivated": "动态域路由已停用", "dynamicDomainsLabel": "动态域路由", "parentDomains": "Parent (inherited) domains", "tags": "标签", "addTag": "添加标签", "saveTags": "保存标签", "resetTags": "重置标签", "tagsSaved": "标签已保存", "tagsReset": "标签已重置", "allowedChildDomains": "子实体允许的静态域名", "selectAllowedDomains": "选择允许的域名", "saveAllowedChildDomains": "保存子实体允许的域名", "resetAllowedChildDomains": "重置子实体允许的域名", "allowedChildDomainsSaved": "子实体允许的域名已保存", "allowedChildDomainsReset": "子实体允许的域名已重置", "selectedDomains": "已选择的域名"}, "PROXY": {"merchantParams": "商家参数", "merchantConfirmation": "您确定要保存商家代理吗？", "merchantProxy": "商家代理", "btnSaveProxy": "保存代理", "proxyTabName": "代理", "titleProxyAdd": "添加新代理", "btnAddProxy": "添加", "btnAddNewProxy": "添加新代理", "btnCancelProxy": "取消", "btnSaveChanges": "保存更改", "tittleProxy": "标题", "decsriptionProxy": "描述", "urlLabel": "URL", "descriptionLabel": "描述", "urlPlaceholder": "URL格式：https://example.com", "labelNotSet": "未设置", "btnSet": "已设置", "labelSelectProxyForEntity": "为实体选择代理", "placeholderSelectProxy": "选择代理······", "btnRemove": "Remove"}, "RATES": {"tabname": "奖励金币兑换率", "bonusCoinRatesTitle": "奖励金币兑换率", "btnSave": "保存更改", "bnsRequiredForPromotions": "Bonus Coin balance required for promotions", "NOTIFICATIONS": {"bonusCoinsRatesSaved": "已成功保存奖励金币兑换率！"}}, "ADDITIONAL": {"additional": "额外设置", "selectType": "选择类型", "unfinished": "未结束", "all": "全部", "paymentRetrySettings": "支付重试设置", "minPaymentRetryTimeout": "支付重试暂停时间下限（分钟）", "maxRetryAttempts": "最大重试次数", "maxSessionTimeout": "最长游戏暂停时间（分钟）", "maxPaymentRetryAttempts": "最大支付重试次数", "gameLogoutOptions": "游戏登出选项", "notificationPaymentRetry": "已成功更新最大支付重试次数", "notificationPaymentRetrySettings": "支付重试设置已成功更新", "notificationLogoutOptions": "已成功保存游戏登出选项"}, "GAME_GROUP": {"tabName": "Game Groups", "description": "Description", "descriptionPlaceholder": "Enter game group description", "id": "Id", "isDefault": "<PERSON><PERSON><PERSON>", "inherited": "Inherited", "isDefaultPlaceholder": "Default game group", "actions": "Actions", "name": "Name", "namePlaceholder": "Enter game group name", "notificationDefaultGameGroupChanged": "{{gameGroup}} game group was successfully set as default", "notificationCreated": "\"{{gameGroup}}\" game group was successfully created", "notificationEdited": "\"{{gameGroup}}\" game group was successfully updated", "removeGameGroupMessage": "Do you really want to remove \"{{gameGroup}}\" game group?", "gameGroupRemoved": "\"{{gameGroup}}\" game group was successfully removed", "editGameGroupTooltip": "Edit game group", "removeGameGroupTooltip": "Remove game group", "createGameGroupTitle": "Create Game Group", "editGameGroupTitle": "Edit Game Group", "addGameGroup": "Add Game Group", "FORM": {"gameGroup": "Default game limits group"}, "MODAL": {"confirmMessage": "Do you really want to delete this game group?", "labelAddFlag": "Add force flag", "messageDefaultGameGroup": "Game group is used as default. Operation requires force flag.", "messageFlag": "If you want to delete it please add force flag."}}, "GAME_GROUP_FILTERS": {"tabName": "Game Limit Filters", "note": "Note, if the same game appear in two different filters of the same game group and currency, the last updated filter will be applied", "GRID": {"id": "#", "createdAt": "Date Created", "updatedAt": "Date Updated", "gameGroup": "Game Group", "defaultGroupLabel": "Default group", "minTotalBet": "Min Total Bet", "maxTotalBet": "Max Total Bet", "maxExposure": "Max Exposure", "defaultTotalBet": "Default Total Bet", "winCapping": "Win Capping", "currencies": "Currencies", "games": "Games", "inherited": "Inherited", "newFilter": "New Filter", "editTooltip": "Edit Game Limits Filter", "deleteTooltip": "Delete Game Limits Filter", "confirmMessage": "Do you really want to delete this Game Limits Filter?"}, "FORM": {"createGameGroupFilterTitle": "Add Game Limits Filter", "editGameGroupFilterTitle": "Edit Game Limits Filter", "gameGroup": "Game Group", "minTotalBet": "Min Total Bet (in Euro)", "maxTotalBet": "Max Total Bet (in Euro)", "maxExposure": "Max Exposure (in Euro)", "defaultTotalBet": "Default Total Bet (in Euro)", "winCapping": "Win Capping (in Euro)", "currencies": "Currencies", "appliesToAllCurrenciesLabel": "Filter applies to all currencies", "appliesToSpecificCurrenciesLabel": "Filter applies to specific currencies", "games": "Games", "appliesToAllGamesLabel": "Filter applies to all slot games", "appliesToAllGamesLabelNote": "Note! the filter will not be applied to the following games: ", "appliesToSpecificGamesLabel": "Filter applies to specific slot games", "appliesToSpecificGamesLabelNote": "Note, special games which their limits cannot be customized do not appear in the list below"}, "NOTIFICATIONS": {"created": "Game Limits Filter successfully created", "updated": "Game Limits Filter successfully updated", "deleted": "Game Limits Filter successfully deleted"}}, "LABELS": {"title": "Entity Labels", "labelsGroup": "Labels Group", "labels": "Labels", "addLabelsGroup": "Add labels group", "updated": "Entity labels successfully updated"}, "RTP_REDUCER": {"tabName": "RTP Reducer", "entity": "Entity", "inheritedForm": "Inherited from", "changeDateTime": "Change Date Time", "gameName": "Game name", "gameCode": "Game code", "rtpBefore": "% Theoretical RTP", "rtpAfter": "% New RTP", "rtpDeduction": "% RTP Deduction", "modalTitle": "New RTP Reduction - {{ title }}", "alertModalRtp": "This action will affect {{ title }} and all of its descendants", "selectGamesChange": "Select the games to change", "selectRtpReduce": "Select the RTP to reduce", "btnCreateRtp": "New RTP configuration", "warningRtp": "List of games with incorrect RTP", "updatedRtd": "New RTP deduction was applied", "removerRtd": "RTP deduction was removed", "filterTitle": "Game name / code", "showChanges": "Show changes only", "MODAL": {"gameName": "Game name", "gameCode": "Game code", "currentRtp": "Theoretical RTP", "newRtp": "New RTP", "resetMessage": "Do you really want to reset RTP for {{ title }} ?"}}, "ENGAGEMENT": {"tabName": "Engagement", "save": "Save", "PAYMENT_SETTINGS": {"tabName": "Payments Settings", "title": "Bonus Payment Settings", "placeholder": "Bonus Payment Method", "inherited": " (inherited)"}, "SUPPORTED_PAYMENT_SETTINGS": {"placeholder": "Supported Bonus Payment Method"}, "BONUS_COIN_SETTINGS": {"tabName": "Bonus Coin Settings", "title": "Bonus Coin Settings", "conversionRate": "Conversion Rate"}, "NOTIFICATIONS": {"paymentMethodChanged": "Payment method was changed successfully!"}}, "BREADCRUMBS": {"selectEntity": "Select Entity", "noEntities": "No entities found", "copy": "Copy path to selected entity", "collapse": "Collapse breadcrumbs"}, "TEST_PLAYERS": {"tabName": "Test Players", "GRID": {"code": "Player Code", "source": "Source", "startDate": "Start Date", "endDate": "End Date", "newTestPlayer": "New Test Player", "support": "Support", "integration": "Integration", "deleteTestPlayer": "Delete Test Player"}, "createTestPlayer": "Create Test Player", "editTestPlayer": "Edit \"{{code}}\" Test Player", "code": "Player Code", "startDate": "Start Date", "endDate": "End Date", "notificationCreated": "Test player was successfully created", "notificationEdited": "\"{{code}}\" test player was successfully updated", "codeShouldBeUnique": "Player code should be unique per entity", "autoCreateTestJackpot": "Auto create test jackpot instances", "playersAreSuccessfullyMarkedAsTest": "All players of entity were successfully marked as test players", "playersAreNoLongerMarkedAsTest": "All players of the entity are no longer marked as test players", "autoCreateTestJackpotStrategyWasSetSuccessfully": "Strategy for test jackpots creation was successfully set to auto", "autoCreateTestJackpotStrategyWasUnsetSuccessfully": "Auto strategy for test jackpots creation was successfully unset", "maxTestPlayers": "Maximum test players", "changeMaxTestPlayersConfirmation": "Do you really want to save maximum test players amount?", "maxTestPlayersEditSuccess": "Maximum test players amount was successfully edited", "maxTestPlayersSmallerThanTotal": "Maximum test players value shouldn't be smaller than the total number of test players the entity has", "deleteTestPlayerConfirmation": "Do you really want to delete \"{{playerCode}}\" test player?", "testPlayerSuccessfullyDeleted": "\"{{playerCode}}\" test player was successfully deleted"}, "toBusinessStructure": "至商业结构", "placeholderChooseOption": "选择任意选项"}, "EMAIL_TEMPLATE": {"tabName": "Email Templates", "templateUpdated": "Template was successfully updated"}, "PROMO": {"CUSTOMER_BULK_ACTIONS": {"applyBonusCoins": "应用奖励金币", "applyFreeBets": "应用免费投注"}, "NOTIFICATIONS": {"PROMO_CREATED": "已成功创建{{ title }} 推广活动", "PROMO_UPDATED": "已成功更新{{ title }} 推广活动"}, "STEPS": {"details": "推广详情", "freeBetsSetup": "免费投注设置"}, "MODALS": {"currency": "货币", "promoName": "推广名称", "promoType": "推广类型", "startDateTme": "开始日期/时间", "endDateTme": "结束日期/时间", "freeSpinsAmount": "免费投注数量", "btnAddGames": "添加游戏", "btnApplySelected": "应用已选项", "placeholderSelectGames": "请选择一个游戏…", "selectGames": "选择游戏", "pleaseAddGames": "请添加一些游戏", "errorGamesRequired": "游戏为必选项", "gameName": "游戏名称"}, "REPORTS": {"playerCode": "玩家ID", "title": "标题", "receivedAt": "收到日期", "amountReceived": "所收数量", "amountAwarded": "所奖数量", "amountRemaining": "剩余数量", "expirationDate": "失效日期", "status": "状态", "addExtraReward": "添加更多奖励", "ADD_MORE_REWARDS": {"title": "添加更多奖励", "addMoreReward": "添加更多奖励", "prolongExpiration": "延长奖励金币兑现有效期", "changesPreview": "修改预览", "appliedChanges": "已应用的修改", "updatedExpirationDate": "已更新的失效日期", "updatedAmountAwarded": "已更新的已奖金额", "updatedAmountRemaining": "已更新的剩余金额", "rewardWasApplied": "该奖励已成功应用于玩家{{code}}", "pleaseNoteDelayInReport": "已应用您的修改。提示：报告将会在1小时内更新，更新后您将看到所有已应用的修改。"}}, "EDIT": {"GENERAL": {"name": "添加更多奖励", "description": "Game Description", "defaultInfo": "De<PERSON>ult Info", "title": "通用", "placeholderTitle": "请输入标题...", "placeholderDescription": "请添加描述...", "startDate": "开始日期", "endDate": "结束日期", "funding": "Funding", "HINTS": {"funding": "Operator funding: BNS transactions are included in GGR real money reports.\nSkywind funding: BNS transactions are excluded from GGR real money reports.", "operator": "所选实体的主实体将拥有管理推广的权限。"}}, "REWARDS": {"title": "奖赏", "type": "奖赏类型", "typeNotSet": "未设置", "noneSelected": "无已选项", "rewardAmount": "奖赏金额", "rewardAmountPerPlayer": "每名玩家的奖励数额", "amount": "金额", "percent": "百分比", "rewardBonus": "奖赏红利", "rewardLimit": "奖赏限额", "minLimit": "下限", "maxLimit": "上限", "in": "后", "on": "", "per": "每", "days": "天", "rewardProcessing": "奖赏处理", "expiration": "失效", "expirationPeriodType": "有效期限类型", "qualifyingGames": "适用的游戏", "PLACEHOLDERS": {"egNorM": "例如{{ n }}或{{ m }}", "egN": "例如{{ n }}"}, "HINTS": {"rewardBonus": "** Test text, please replace", "rewardAmount": "在推广活动中奖励给玩家的金额", "rewardLimit": "** Test text, please replace", "payoutLimitPerPlayer": "限制玩家的营销赔付", "promoPayoutLimit": "显示所有玩家在限定的时段内能够从该推广中获得的营销赔付的金额上限", "maxConversionAmount": "** Test text, please replace", "expiration": "推广失效后，玩家所有尚未使用的奖励都将丢失，推广对其而言也将结束。", "expirationBNS": "自获得奖励金币起至奖励金币失效，玩家将有这么多天或小时的时间来使用奖励金币。", "wageringMultiplier": "** Test text, please replace", "numberOfFreeBets": "推广中每名玩家得到的免费投注的数量", "coinValue": "该数值根据当前的兑换率计算得出", "playThrough": "玩家在取出营销赔付金额之前需投入相当于该金额多少倍的赌金", "payoutSchedule": "设置奖励赔付频率", "numberOfBonusCoins": "将向每名玩家奖励多少奖励金币 ", "minCoinsToRedeem": "玩家如要将奖励金币兑换成真钱，需首先拥有这个数量的奖励金币", "maxCoinsToRedeem": "Set the max amount of BNS each player is allowed to redeem. Any BNS winnings above this amount will be capped."}, "VIRTUAL_MONEY": {"maxConversionAmount": "最大兑换金额", "wagering": "虚拟金钱下注额", "multiplier": "翻倍乘数", "depositAmount": "投注金额", "bonusAmount": "红利金额", "hintText": "玩家无法取出虚拟金钱。若要取出虚拟金钱，玩家必须先将其兑换为真钱。兑换会在玩家达到红利下注额要求后即刻自动完成。"}, "FREEBET": {"numberOfFreeBets": "免费投注数量", "coinValue": "币值", "hintText": ""}, "REBATE": {"rewardCurrecy": "奖赏货币", "pleaseSelect": "请选择", "type": "类型", "typeGGR": "% 来自于游戏毛收入（投注-赢奖）", "typeNGR": "% 来自于游戏净收入(投注-赢奖-营销支付)", "typeMinMax": "最小或最大投注", "payoutPeriodTypeHours": "小时", "payoutPeriodTypeDays": "天", "payoutPeriodTypeWeeks": "周", "payoutPeriodTypeMonths": "月", "playThrough": "下注要求", "payoutSchedule": "赔付日程", "intervalTypeDaily": "每日", "intervalTypeWeekly": "每周", "intervalTypeMonthly": "每月", "lastDayOfMonth": "每月最后一天", "hintText": "", "payoutLimitPerPlayer": "玩家人均赔付限额", "promoPayoutLimit": "推广赔付限额"}, "BONUS_COIN": {"numberOfBonusCoins": "奖励代币数量", "totalValuePreview": "总值预览", "coinValue": "币值", "coinValuesNotDefined": "币值未定义！您需要在实体设置中设置币值。", "minCoinsToRedeem": "可兑现金币的最小数量", "maxCoinsToRedeem": "兑换限额", "realMoneyValue": "真钱金额", "redemptionTitle": "奖励金币兑换", "redemptionSubtitle": "一旦玩家的奖励金币余额超过了兑换下限，玩家即可将奖励金币兑换成真钱。", "estimatedRedeemPercent": "据估计，{{percent}}%的玩家将会兑换奖励金币", "hintText": "奖励代币可在奖金由替代货币（BNS）支付的符合条件的游戏中使用。", "startRewardOnPlayerAdded": "Start expiration when the player is added to the promotion", "startRewardOnGameOpen": "Start expiration on player's 1st game launch"}, "EXPIRATION_PERIOD": {"days": "天", "hours": "小时"}}, "CONDITIONS": {"title": "条件", "hint": "您可通过本界面来建立奖励的触发条件。"}, "QUALIFYING_GAMES": {"title": "适用的游戏", "virtualMoneyCoefficient": "系数", "freebetCoinValue": "币值"}, "PARTICIPANTS": {"title": "参与者", "addParticipants": "添加参与者", "numberOfParticipantsSelected": "已选{{ playercodes.length }}个参与者 ", "unableToParsePlayerCodes": "无法解析玩家代码。请检查您近期的CSV文件并再试一次。", "csvWrongFileType": "文档类型错误！", "csvWrongFileTypeExtra": "只可上传CSV格式的文档", "csvMaxNumberItemsExceeded": "Maximum number of participants ({{ limit }}) exceeded", "csvMaxCsvFileSizeExceeded": "The file size exceeds the allowable limit of", "labelAddCSV": "添加CSV文档", "btnAddSelected": "应用已选项", "csvHintStructure": "请确保CSV文档有\"玩家ID\"这一栏。其包含将会参与该推广活动的玩家代码。", "downloadSampleCSV": "下载CSV文件示例", "fileWithPlayersCount": "含{{ length }}名玩家", "unableToParseCSV": "无法解析CSV文件", "removeThisCSV": "移除该CSV文件", "bonusCoinsBalance": "奖励金币余额", "rewardPerPlayer": "平均每名玩家的奖励", "currentParticipants": "当前参与者", "participantsRevoked": "已退出的参与者", "numberOfPlayersToSave": "需保存的玩家人数", "maxNumberOfPlayers": "玩家人数上限", "rewardValueNotDefined": "未定义的值", "rewardValueNotDefinedHint": "请在“奖励”区域为“奖励金币数量”设定一个值", "numberOfDuplicatesIgnored": "已忽略{{ length }}个复制的玩家代码", "insufficientBalance": "您的账户中没有足够的奖励金币余额来参与奖励金币推广活动。\n请联系您的Skywind分销商获取详情。", "removeParticipant": "移除", "maximumLimitExceeded": "已超过最大限额", "participantRemoved": "参与者已被移除出推广", "removeParticipantPrompt": "您确定要将玩家**{{ playerCode }}** 从当前推广中移除吗？", "removeParticipantPromptForce": "You are about to remove player **{{ playerCode }}** from current promotion! This action requires a force flag. Are you sure?", "errorPlayerHasPromo": "玩家已有激活的@推广@", "errorParticipantsGeneral": "Unable to add participants"}, "promotion": "推广", "panelInfo": "信息", "panelReports": "报告", "addParticipants": "添加参与者", "projected": "推算", "actual": "实际", "created": "已创建", "by": "创建者", "createdBy": "创建者为", "reach": "达到", "participants": "参与者", "payout": "赔付", "linkBonusCoinsPerformance": "奖励金币绩效报告", "linkPromotionParticipants": "推广参与者报告", "linkRedeemableExpiredCoins": "已失效可兑换金币的报告"}, "GRID": {"title": "推广名称", "id": "ID", "type": "类别", "status": "状态", "state": "状态", "labels": "标签", "participants": "参与者", "totalPayout": "总赔付", "startDateTime": "开始日期/时间", "endDateTime": "结束日期/时间", "createdDate": "创建日期", "owner": "Owner"}, "STATE": {"new": "新", "pending": "待定", "alt_pending": "新", "inProgress": "进行中", "in_progress": "进行中", "alt_in_progress": "进行中", "finished": "已结束", "alt_finished": "已结束", "expired": "已过期"}, "STATUS": {"active": "已激活", "inactive": "未激活"}, "REWARD_STATUS": {"not_started": "新", "awarded": " Received promotion", "started": "Used promotion", "expired": "Expired", "redeemed": "Redeemed", "finished": "Finished", "redeemable_expired": "Redeemable expired", "pending": "Pending", "confirmed": "Confirmed", "revoked": "Revoked"}, "titlePromoDetails": "推广详情", "titleCreatePromotion": "创建推广", "titleEditPromotion": "管理推广", "titleViewPromotion": "查看推广", "titleClonePromotion": "Clone Promotion", "titlePromosDisabled": "已禁用推广", "freebet": "免费投注", "freebetSimple": "一般免费投注", "rebate": "退款", "virtualMoney": "虚拟金钱", "bonusCoin": "奖励金币", "btnCreatePromo": "创建推广", "btnCreatePromoNew": "创建推广(新)", "btnCreateSimpleFreeBet": "创建一般免费投注", "activatePromo": "激活", "deactivatePromo": "停用", "clonePromo": "<PERSON><PERSON>", "activatedNotification": "已成功激活\"{{title}}\"推广活动！", "deactivatedNotification": "已成功停用\"{{title}}\"推广活动！", "merchantPromotionsDisabled": "由于您的商家不支持推广，推广对您不适用。欲知详情，请联系客服。", "unableToSelectMerchant": "所选商家不可进行推广活动。请联系客服，以启用\"内部推广\"", "error": "错误"}, "MANAGEKEYS": {"Label": "", "accessKey": "访问钥匙", "add": "添加新钥匙", "confirmDelete": "您真的要删除钥匙吗？", "processedWithSelectedKey": "使用已选择的钥匙继续登录", "selectKey": "选择钥匙", "submit": "添加", "title": "钥匙管理"}, "SETTINGS": {"title": "Settings", "timezone": "时区", "pageSize": "页面大小", "theme": "选择客户主题", "dateFormat": "选择日期格式", "timeFormat": "选择时间格式", "currencyFormat": "选择货币格式", "currencyFormatDefault": "默认设置", "reset": "恢复默认设置", "apply": "应用", "pleaseProvideAappToken": "请提供操作代币", "changeTwofa": "更改双重认证方式", "enabledTwofaTypes": "启用双重认证类型", "btnAddNewTwofaType": "添加新的双重认证类型", "defaultTwofaType": "默认类型", "markAsDefaultTwofaType": "设为默认", "successTwofaMarkedAsDefault": "已将认证类型\"{{ name }}\"设为默认", "successTwofaTypeAdded": "已成功添加认证类型\"{{ name }}\"", "notificationSuccess": "已保存移动应用程序设置"}, "SIDEBAR": {}, "COMPONENTS": {"NOTIFICATIONS": {"titleSuccess": "成功！", "status_inactive": "状态已成功设置为“已激活”", "status_active": "状态已成功设置为“未激活”"}, "WIZARD": {"btnPrev": "前一步", "btnNext": "下一步", "btnSubmit": "提交"}, "GRID": {"EMPTY_LIST": "无搜索结果", "TOTAL": "总", "COLLAPSE": "崩溃", "RELOAD": "重新加载", "LOADING": "缓冲...", "RESULT_DOANLOAD_CSV": "下载CSV", "RESULT_PRINT": "打印当前页", "RESULT_EXPORT": "导出当前页", "RESULT_EXPORT_CSV": "导出CSV", "RESULT_OPEN_NEW_TAB": "新标签页中打开", "RESULT_EXPORT_FILENAME": "导出{title} {date} (第{page}页)", "GRID_OPEN_DETAILS_POPUP": "查看", "GRID_TOGGLE_FILTER": "筛选", "GRID_COLUMNS_VISIBILITY": "列可见性", "GRID_ROW_ACTIONS": "转轴", "ITEMS_FOUND": "items found"}, "PAGINATION": {"one_item_found": "搜索结果", "many_items_found": "搜索结果", "next": "下一页", "previous": "上一页"}, "MULTISELECT": {"multiple": "多个值", "search": "搜索"}, "MENU_SELECT": {"clear": "清除", "cancel": "取消", "apply": "应用", "search": "搜索"}, "COLUMNS_MANAGEMENT": {"tooltip": "列管理", "menuTitle": "显示列"}, "DATERANGE": {"today": "今天", "yesterday": "昨天", "last3Days": "过去3天", "last7Days": "过去7天", "thisMonth": "本月", "prevMonth": "上月", "customPeriod": "自定义时段"}, "DATE_RANGE": {"today": "Today", "yesterday": "Yesterday", "last3Days": "Last 3 days", "last7Days": "Last 7 days", "last14Days": "Last 14 days", "last30Days": "Last 30 days", "last90Days": "Last 90 days", "next7Days": "Next 7 days", "next14Days": "Next 14 days", "next30Days": "Next 30 days", "next90Days": "Next 90 days", "thisMonth": "This month", "prevMonth": "Previous month", "lastMonth": "Last month", "nextMonth": "Next month", "monthToDate": "Month to date", "customPeriod": "Custom period", "clear": "Clear", "cancel": "Cancel", "apply": "Apply"}, "DATE_PICKER": {"clear": "Clear", "cancel": "Cancel", "apply": "Apply"}, "GAMES_SELECT_MANAGER": {"chooseTheGames": "Select the games to change", "view_all": "View all", "view_selected": "View selected", "selectedGamesTitle": "选择游戏", "availableGamesTitle": "游戏", "availableLabelsTitle": "标签", "searchGamesPlaceholder": "按游戏名称、代码或标签搜索", "searchLabelsPlaceholder": "按标签名称搜索", "btnAddGames": "添加", "btnRemoveGames": "移除", "btnIntersect": "添加标签组", "selectAll": "全选", "inlineGamesCount": "{{count}}个游戏", "countGamesInIntersection": "标签组中有{{count}}个游戏", "countGamesInSelectedLabels": "已选标签下有{{count}}个游戏", "btnAddIntersection": "添加标签组({{count}} 游戏)", "gamesNotFound": "搜索不到游戏", "noGamesToShow": "就是这些了，没有更多游戏了。", "game": "游戏", "itemNotAvailable": "条目{{ id }}已经不再可用", "emptySelectedGames": "Add games to this category using search", "PREVIEW": {"title": "此分类中的游戏。预览", "subtitle": "此分类中的游戏{{count}}", "btnCancel": "取消", "btnApply": "应用"}}, "CONDITIONS": {"allRules": "以下所有规则", "anyRules": "以下规则中的任意一条", "clearAll": "清除全部", "addCondition": "添加条件", "addGroup": "添加组", "placeholderValueField": "值域...", "placeholderOperator": "运营商...", "placeholderValue": "值"}, "BULK_ACTIONS": {"messageConfirm": "您确定要进行该操作吗？", "messageLess": "仅有{{availableRowsNumber}}/{{checkedRowsNumber}}行可用于执行该操作。您确定要继续吗？", "messageData": "没有可用于执行该操作的数据。", "btnYes": "是", "btnNo": "否", "btnOk": "好的"}, "BO_SELECT": {"placeholderSearch": "搜索条目", "placeholder": "选择条目"}, "WIDGET": {"andMore": "and {{number}} more"}}, "GAME_CATEGORIES": {"myCategories": "我的分类", "btnNew": "新", "btnNewCategory": "新分类", "btnPreview": "预览", "btnSave": "保存", "textEmptyCategories": "暂无可以显示的内容。", "textPleaseClickNew": "若要开始创建大厅内容，请点击\"新分类\"", "categoryName": "分类名称", "gamesInCategory": "此分类中的游戏", "emptySelectedGames": "使用搜索来将游戏添加至此分类", "emptySelectedGamesAlter": "无已选游戏", "deleteCategoryConfirm": "您真的要删除分类吗？", "selectLanguage": "选择语言", "chooseIcon": "选择图标(SVG)", "NOTIFICATIONS": {"created": "分类 \"{{title}}\"已成功创建！", "saved": "分类\"{{title}}\"已成功保存！", "deleted": "分类\"{{title}}\"已成功删除！", "removeTab": "您真的要移除此选项卡吗？"}}, "HINTS": {"GAME_CATEGORIES": {"buildContentByManageCategories": "通过管理分类和添加游戏来创建您的大厅内容。您可将游戏分别加入对应分类，也可通过添加标签来同时查看该标签下的所有游戏。", "inheritedCategories": "<b>Note:</b> The list of categories is inherited from parent"}, "btnGotIt": "知道了", "btnCloseHint": "关闭"}, "VALIDATION": {"dateGreaterThan": "所选日期早于{{value}}", "dateGreaterThanNow": "所选日期需为未来的某个日期", "domainHttp": "URL不可包含协议部分", "domainUrlParts": "URL需包含2个及以上等级", "JSON": "无效的JSON数据：{{error}}", "JSONinvalid": "无效的JSON数据", "invalidCreditCard": "是无效的信用卡号码", "invalidEmailAddress": "电子邮箱地址无效", "invalidPassword": "密码无效。密码需包含至少8个字符，至少1个小写英文字母，至少1个大写英文字母，以及至少1个数字。仅允许拉丁字符。", "invalidDomain": "域名无效", "invalidIPv4Address": "IPv4地址无效", "invalidIPv4AddressMask": "无效的IPv4地址或掩码", "invalidPhoneNumberMask": "无效的电话号码。号码只能包含数字0到9，可在号码开头附加符号（如+1234567890或123567890）。", "invalidDigitsOnly": "无效值。只可填数字。", "invalidNumbersOnly": "无效值。只可输入数字。", "invalidIssueId": "Invalid issue id", "min": "最小为{{min}}", "max": "最大为{{max}}", "minLength": "最小长度为{{min}}", "maxLength": "Maximum length is {{max}}", "minLengthArray": "条目最低数量为{{value}}", "notEquals": "错误值", "notEqualsPassword": "您所提供的密码不被接受", "notEqualsString": "值{{value}}不被接受", "passwordNotEquals": "密码不匹配", "passwordMinLength": "密码长度不满足要求", "passwordContainDigit": "密码需包含至少{{value}}个数字", "passwordContainLowercase": "密码需包含至少{{value}}个小写英文字母", "passwordContainUppercase": "密码需包含至少{{value}}个大写英文字母", "passwordNotMatch": "The new password and the new password confirmation don't match", "required": "必填项", "coeffsRequired": "Coefficients are mandatory", "invalidLatinCharsDigitsSymbols": "该值只可包含以下字符：0-9 a-z A-Z _ - : .", "startAndEndDate": "开始日期需早于结束日期", "urlIsNotCorrect": "不接受URL地址。", "fileFormatNotSupported": "File format not supported", "spacesAreNotAllowed": "Spaces are not allowed", "invalidColorHexFormat": "颜色必须为HEX格式（＃000000或＃000）", "invalidCustomerId": "Length should be between 6 and 30 symbols. Spaces are not allowed.", "validateFirstMaxValueIfSecondSetWithValueDynamic": "Field {{firstDisplayName}} should be lower than {{value}} or equal due to {{secondControlValue}} in {{secondDisplayName}}", "validateFirstMaxValueIfSecondSetWithValue": "由于到期时间类型为每天，到期时间字段应小于或等于{{value}}", "invalidStakeAll": "Invalid format. Only positive numbers >= 0.01 separated by ', ' are allowed", "invalidLiveStakeAll": "Invalid format. Only positive numbers >= 0.01 separated by ' ; ' are allowed", "invalidStakeDef": "Stake Def should be from Coin Bets values", "limitsCanNotCloneThemselves": "Game limits can't clone themselves", "valuesCanNotBeDuplicated": "Values can't be duplicated", "invalidFormat": "Invalid format. Name cannot start with \"http://\" or \"https://\"", "aamsCodeError": "AAMS code in settings is required for Italian regulation. Only [0-9] numbers are allowed.", "valuesAreNotTheSame": "Values are not the same", "positiveNumbers": "Value should be positive number", "stakeAllOutOfRange": "Coin Bets values should be from {{ range }} range", "fractionsNumbersLength": "Max 2 decimal digits are allowed", "minLowerThanMax": "min should be < max", "lowerThan": "Should be lower than {{value}}", "greaterThan": "Should be greater than {{value}}", "schemaDefinitionIsIncorrect": "Format of schema definition is incorrect", "greaterOrEqual": "{{min}} should be <= {{max}}", "noDecimals": "No decimals are allowed", "valueShouldBeUnique": "Value should be unique", "positiveInteger": "Value should be a positive integer"}, "BACKEND_ERRORS": {"STATUS_400__CODE_101": "这不是一个品牌", "STATUS_400__CODE_103": "限额错误", "STATUS_400__CODE_107": "无效的游戏支付", "STATUS_400__CODE_108": "已达到最大交易容量", "STATUS_400__CODE_109": "该操作仅限于品牌或商家", "STATUS_400__CODE_151": "组行为的目标项目过多", "STATUS_400__CODE_152": "错误的行为请求", "STATUS_400__CODE_199": "该电子邮件地址已被使用", "STATUS_400__CODE_207": "提供者的用户名、密码或密钥缺失", "STATUS_400__CODE_214": "该游戏不属于该游戏组中", "STATUS_400__CODE_217": "更新状态的请求无效", "STATUS_400__CODE_218": "更新代理商的请求无效", "STATUS_400__CODE_226": "更新令牌状态的请求无效", "STATUS_400__CODE_228": "新密码需与原密码不同", "STATUS_400__CODE_229": "'{{permission}}' 权限在该列表中不存在", "STATUS_400__CODE_303": "更新状态的请求无效", "STATUS_400__CODE_306": "该游戏被冻结", "STATUS_400__CODE_307": "游戏类别不能为空", "STATUS_400__CODE_308": "若要添加游戏到实体中，需支付参数使用费", "STATUS_400__CODE_311": "该游戏提供者被冻结", "STATUS_400__CODE_320": "开始游戏令牌出现错误", "STATUS_400__CODE_321": "开始游戏令牌已过期", "STATUS_400__CODE_322": "游戏令牌错误", "STATUS_400__CODE_323": "游戏令牌已过期", "STATUS_400__CODE_328": "终端更新请求错误", "STATUS_400__CODE_329": "终端获取请求无效", "STATUS_400__CODE_330": "大厅更新请求错误", "STATUS_400__CODE_331": "该大厅无法删除，因已被分配至终端: {{terminals}}", "STATUS_400__CODE_340": "无法找到该通知的接收者", "STATUS_400__CODE_341": "无法找到该通知", "STATUS_400__CODE_40": "验证错误：{{messages}}", "STATUS_400__CODE_403": "筛选无效", "STATUS_400__CODE_41": "该电子邮件地址格式无效", "STATUS_400__CODE_42": "玩家代码需包含6至30个字符", "STATUS_400__CODE_43": "您所提供的密码无效", "STATUS_400__CODE_501": "不支持该商家类别", "STATUS_400__CODE_503": "商家品牌不支持该操作", "STATUS_400__CODE_504": "这不是一个商家品牌", "STATUS_400__CODE_61": "无法找到该母实体", "STATUS_400__CODE_62": "母实体之一被冻结", "STATUS_400__CODE_622": "更新角色的请求无效", "STATUS_400__CODE_625": "无法将角色添加至玩家 {{reason}}", "STATUS_400__CODE_64": "该实体非空白！", "STATUS_400__CODE_669": "为建立标签记录", "STATUS_400__CODE_670": "无法找到该支付方法", "STATUS_400__CODE_671": "无法找到该支付方法类别", "STATUS_400__CODE_672": "无法找到交易来实施该操作", "STATUS_400__CODE_674": "有缺陷的JSON：{{reason}}", "STATUS_400__CODE_675": "无效的支付行为属性：{{attribute}}", "STATUS_400__CODE_676": "无效的支付行为类别：{{action}}", "STATUS_400__CODE_677": "支付行为值为负", "STATUS_400__CODE_678": "支付行为列表为空", "STATUS_400__CODE_685": "免费投注余额不足", "STATUS_400__CODE_686": "免费投注无效", "STATUS_400__CODE_703": "无法解析IP地址{{ip}}", "STATUS_400__CODE_705": "未能获得外部游戏URL", "STATUS_400__CODE_709": "玩家不再拥有该推广", "STATUS_400__CODE_710": "无法对该推广类别执行此操作", "STATUS_400__CODE_711": "该推广不在有效状态", "STATUS_400__CODE_712": "该玩家被冻结", "STATUS_400__CODE_713": "还未确认密码", "STATUS_400__CODE_714": "密码需与用户名不同", "STATUS_400__CODE_715": "禁止从未授权的网站启动游戏", "STATUS_400__CODE_716": "JPN请求错误", "STATUS_400__CODE_81": "您无法移除默认国家", "STATUS_400__CODE_82": "该国家不在列表上", "STATUS_400__CODE_83": "该国家不存在于母实体中", "STATUS_400__CODE_86": "您无法移除默认货币", "STATUS_400__CODE_87": "该货币不在列表上", "STATUS_400__CODE_88": "该货币不存在于母实体中", "STATUS_400__CODE_89": "该货币不存在", "STATUS_400__CODE_90": "该数额非负数", "STATUS_400__CODE_91": "余额不足", "STATUS_400__CODE_92": "实体余额不足", "STATUS_400__CODE_94": "您无法移除默认语言", "STATUS_400__CODE_95": "该语言不在列表上", "STATUS_400__CODE_96": "该语言不存在于母实体中", "STATUS_400__CODE_97": "国家不为数组", "STATUS_400__CODE_98": "货币不为数组", "STATUS_400__CODE_99": "语言不为数组", "STATUS_401__CODE_10": "访问令牌缺失", "STATUS_401__CODE_201": "密码不匹配", "STATUS_401__CODE_202": "用户名与密码不匹配", "STATUS_401__CODE_203": "密码不正确", "STATUS_401__CODE_204": "访问令牌错误", "STATUS_401__CODE_205": "访问令牌已过期", "STATUS_401__CODE_208": "终端令牌错误", "STATUS_401__CODE_209": "提供者的密钥不正确", "STATUS_401__CODE_221": "密码重置链接已过期", "STATUS_401__CODE_222": "密码重置已完成，或出现了其他错误", "STATUS_401__CODE_223": "系统中没有储存该玩家的密码", "STATUS_401__CODE_224": "密码未更改", "STATUS_401__CODE_225": "玩家信息未更改", "STATUS_401__CODE_716": "您所提供的验证码不正确", "STATUS_401__CODE_717": "双重认证令牌已失效", "STATUS_401__CODE_718": "双重认证令牌错误", "STATUS_401__CODE_719": "发送短信时发生错误。请检查日志", "STATUS_401__CODE_720": "发送电子邮件时发生错误。请检查日志", "STATUS_401__CODE_721": "您所选择的双重认证方式不被允许", "STATUS_401__CODE_722": "双重认证未配置", "STATUS_401__CODE_723": "双重认证验证码未生成或已失效", "STATUS_403__CODE_206": "被禁止", "STATUS_403__CODE_219": "无法更新已存档的推广", "STATUS_403__CODE_50": "这不是首要实体", "STATUS_403__CODE_626": "您无法管理该角色", "STATUS_403__CODE_701": "该IP所在国家{{country}}：{{ip}}受限", "STATUS_403__CODE_702": "IP:{{ip}}的货币{{currency}}受限", "STATUS_403__CODE_706": "无法更新非待定的推广", "STATUS_403__CODE_707": "该品牌的测试玩家数已超出上限", "STATUS_404__CODE_102": "无法找到该玩家", "STATUS_404__CODE_104": "无法找到该货币的限额", "STATUS_404__CODE_105": "无法找到该环节", "STATUS_404__CODE_106": "该环节已结束", "STATUS_404__CODE_110": "无法找到该白名单", "STATUS_404__CODE_202": "该用户不存在", "STATUS_404__CODE_211": "无法找到该游戏组", "STATUS_404__CODE_213": "该游戏于该实体为不可得", "STATUS_404__CODE_215": "代理商无该域名记录", "STATUS_404__CODE_227": "该网站令牌不存在", "STATUS_404__CODE_300": "无法找到该游戏", "STATUS_404__CODE_304": "无法找到该游戏类别", "STATUS_404__CODE_312": "无法找到该游戏提供者", "STATUS_404__CODE_324": "无法找到该大厅", "STATUS_404__CODE_327": "无法找到该终端", "STATUS_404__CODE_343": "无法找到该网站", "STATUS_404__CODE_502": "无法找到该终端", "STATUS_404__CODE_51": "无法找到该实体", "STATUS_404__CODE_600": "无法找到该交易", "STATUS_404__CODE_623": "该角色不存在", "STATUS_404__CODE_680": "无法找到该推广", "STATUS_404__CODE_681": "无法找到该终端", "STATUS_404__CODE_683": "无法找到游戏历史详情", "STATUS_404__CODE_684": "无法找到 {{item}}", "STATUS_404__CODE_715": "该用户的双重认证未设置", "STATUS_404__CODE_80": "无法找到该国家", "STATUS_404__CODE_85": "无法找到该货币", "STATUS_404__CODE_93": "无法找到该语言", "STATUS_409__CODE_100": "该玩家已存在", "STATUS_409__CODE_111": "该白名单已存在。您可对其进行修补", "STATUS_409__CODE_200": "该用户已存在", "STATUS_409__CODE_210": "该游戏组已存在", "STATUS_409__CODE_212": "该游戏已存在于该游戏组中", "STATUS_409__CODE_301": "该游戏已存在", "STATUS_409__CODE_302": "未能从子实体中删除该游戏，需要强制标志位", "STATUS_409__CODE_305": "该游戏类别已存在", "STATUS_409__CODE_310": "该游戏提供者已存在", "STATUS_409__CODE_325": "该大厅已存在", "STATUS_409__CODE_326": "该终端已存在", "STATUS_409__CODE_342": "该网站已存在", "STATUS_409__CODE_500": "该商家已存在", "STATUS_409__CODE_60": "该实体已存在", "STATUS_409__CODE_624": "未能删除相关联用户的角色，需要强制标志位", "STATUS_409__CODE_63": "该实体正在被编辑！", "STATUS_409__CODE_682": "未能删除该项目，因已被分配至实体。需要强制标志位", "STATUS_500__CODE_1": "内部服务器错误", "STATUS_500__CODE_216": "代理商的品牌ID错误，或该域名已存在", "STATUS_500__CODE_220": "品牌ID错误，或该代码已存在", "STATUS_500__CODE_405": "缺少货币汇率", "STATUS_500__CODE_505": "该商家未正确配置", "STATUS_500__CODE_621": "无法创建该角色", "STATUS_500__CODE_673": "还未做好限制交易的准备。请稍后再试。", "STATUS_500__CODE_700": "IP位置查询错误：{{message}}", "STATUS_500__CODE_717": "JPN内部服务器错误", "STATUS_502__CODE_506": "商家内部错误{{reason}}", "STATUS_502__CODE_507": "在与商家整合的过程中发生错误{{reason}}", "STATUS_502__CODE_672": "无法将资金转入外部钱包", "code": "验证码：", "status": "状态：", "error_code": "错误代码："}, "WEEK": {"Sunday": "周日", "Monday": "周一", "Tuesday": "周二", "Wednesday": "周三", "Thursday": "周四", "Friday": "周五", "Saturday": "周六"}, "ACTIVITY_LOG": {"GRID": {"dateTime": "日期/时间", "activity": "活动", "includeActivity": "Include activity", "excludedActivity": "Exclude activity", "history": "历史", "initiatorType": "启动器类别", "statusCode": "Status Code", "login": "登录", "initiatorService": "启动器服务", "ip": "IP", "system": "系统", "showHistoryTitle": "View history", "entity": "Entity", "eventName": "Event name", "summary": "Summary", "path": "Path", "pathInclude": "Include path (Separated by \",\")", "pathExclude": "Exclude path (Separated by \",\")", "operation": "Operation", "method": "Method", "methodInclude": "Include method", "methodExclude": "Exclude method", "includeSubEntities": "Include sub entities"}, "FILTER": {"customer": "玩家", "user": "用户", "system": "系统", "backoffice": "管理API", "games": "游戏提供者API", "operator": "运营商API", "report": "报告API", "player": "玩家API", "terminal": "终端API", "path": "转销商/运营商", "includeSubEntities": "Include Sub-Entities", "boMapi": "BO (Management API)", "METHOD": {"get": "GET", "post": "POST", "patch": "PATCH", "put": "PUT", "delete": "DELETE", "cron": "CRON", "service": "SERVICE"}, "include": "Include", "dont_include": "Don't include"}, "VIEW_HISTORY_MODAL": {"detailedHistory": "Detailed history", "parameters": "Parameters", "result": "Result", "copyToClipboard": "Copy to clipboard"}, "CSV": {"method": "Method", "eventName": "Event name", "date-time": "Date/Time (GMT {{zone}})", "entity": "Entity", "summary": "Summary", "history": "History", "initiatorType": "Initiator Type", "initiatorName": "<PERSON><PERSON>", "initiatorServiceName": "Initiator service", "ip": "Ip", "userAgent": "System", "actions": "Actions", "path": "Path", "login": "<PERSON><PERSON>", "system": "System"}}, "USERS": {"editUser": "编辑用户", "createUser": "创建用户", "btnClose": "关闭", "btnSave": "保存修改", "GRID": {"entityCode": "实体代码", "entityPath": "Entity path", "fullName": "姓名", "username": "用户名", "userId": "User ID", "role": "角色", "email": "电子邮箱地址", "created": "已创建", "modified": "已修改", "lastLogin": "上一次登录", "status": "状态", "actions": "操作", "password": "密码", "repeatPassword": "重复密码", "edit": "编辑条目", "type": "Type"}, "FILTER": {"firstName": "名", "lastName": "姓", "system": "系统", "active": "已激活", "inactive": "未激活", "locked_by_auth": "Locked", "emailPlaceholder": "<EMAIL>", "boUserType": "BackOffice", "operatorApiUserType": "Operator API", "studioUserType": "Live Studio"}, "CREATE": {"userFormTextFieldSize": "最小长度 {{min}}, 最大长度{{max}}"}, "FORM": {"general": "通用", "name": "姓", "lastName": "名", "username": "用户名", "email": "电子邮箱", "password": "密码", "btnReset": "重置密码", "status": "状态", "active": "已激活", "inactive": "未激活", "role": "角色", "additionalRoles": "额外角色"}, "ROLES": {"list": "角色列表", "btnCreate": "创建角色", "role": "角色", "actions": "操作", "notificationRemoved": "已成功移除该角色！", "notificationEdited": "已成功编辑该角色！", "notificationAdded": "已成功添加该角色！", "edit": "编辑角色", "add": "添加角色", "view": "查看角色", "delete": "Delete role", "MODAL": {"title": "标题", "description": "描述", "shared": "共享的角色", "btnClose": "关闭", "btnSave": "保存修改", "confirmMessage": "您是否确定要删除该角色？", "messageLinkedRole": "该角色与一些用户相关联！ ", "messageFlag": "若要删除该角色，请先添加force flag.", "labelAddFlag": "添加force flag", "btnYes": "是", "btnNo": "否"}}, "CSV": {"entityPath": "Entity path", "entityCode": "Entity code", "name": "Name", "username": "Username", "roles": "Role", "email": "E-mail", "createdAt": "Created (GMT {{zone}})", "updatedAt": "Modified (GMT {{zone}})", "lastLogin": "Last login (GMT {{zone}})", "userType": "Type", "status": "Status"}}, "BUSINESS_STRUCTURE": {"btnExpand": "展开全部", "searchByDomain": "Search by domain", "title": "标题", "code": "代码", "key": "密钥", "regional": "Regional", "currency": "货币", "country": "国家", "gameGroup": "Game group", "jurisdiction": "Juris<PERSON>", "language": "语言", "status": "状态", "actions": "操作", "notificationCreated": "\"{{name}}\" was created successfully", "notificationEdited": "\"{{name}}\" was updated successfully", "editRegionalEntitySettings": "Edit regional {{title}} settings", "MODAL_GENERAL": {"wellDone": "干得漂亮！", "clickHere": "点击此处", "initiateSetup": "{{clickHere}} 来开始设置您的新实体{{entityTitle}}", "settings": "设置", "hint": "提示：您之后可在操作板块点击{{settings}}来继续设置实体", "btnCancel": "取消", "btnSave": "保存", "btnCreate": "创建", "btnClose": "关闭", "title": "标题", "type": "类别", "optionEmpty": "请选择……", "reseller": "转销商", "operatorWallet": "运营商（钱包）", "operatorSeamless": "运营商（无缝）", "name": "名称", "status": "状态", "active": "已激活", "inactive": "未激活", "password": "密码", "serverURL": "服务器URL", "code": "代码", "promotions": "内部推广", "enabled": "已启用", "disabled": "已禁用", "description": "描述", "placeholderTitle": "实体标题", "placeholderName": "实体名称", "playerPrefix": "Player Prefix", "placeholderCode": "实体代码", "storePlayerInfo": "存储玩家信息", "supportedMerchantTypes": "受支持的商家类型", "isPlayerPasswordChangeEnabled": "强制玩家更改密码", "webSiteUrl": "Website URL"}, "MODAL_STATUS": {"setStatus": "设置{{statusTitle}}状态", "changeStatus": "您确定要改变 <b>{{statusTitle}}</b>的状态吗？"}, "WIDGETS": {"andMore": "另有{{number}}", "manageBalance": "管理余额", "loading": "正在加载……", "addCountry": "添加国家", "addLanguage": "添加语言", "active": "已激活", "inactive": "未激活", "maintenance": "维护", "blocked": "Blocked", "blockedByAdmin": "Blocked by <PERSON><PERSON>", "settings": "设置", "addChild": "添加子实体", "move": "移动", "addCascade": "添加游戏级联", "info": "Info", "labels": "Labels", "test": "Test"}, "MOVE_ENTITY": {"title": "Move Entity", "description": "Choose Entity/Brand/Merchant to move and new parent Entity", "entityBrandMerchantToMovePlaceholder": "Entity/Brand/Merchant to move", "newParentEntityPlaceholder": "New parent Entity", "move": "Move", "successNotification": "Entity \"{{entityPath}}\" successfully moved to \"{{newParentPath}}\"", "errorCode": "Error Code"}}, "LOBBY": {"selectOperator": "选择运营商", "selectEntity": "选择实体", "create": "New Lobby", "buildInProgress": "正在创建中……", "messageRemove": "您确定要移除大厅吗？", "notificationCreate": "已成功创建大厅\"{{title}}\" ", "notificationUpdate": "已成功编辑大厅\"{{title}}\" ", "notificationDelete": "已成功删除大厅\"{{title}}\" ", "copy": "Create copy", "edit": "Edit", "delete": "Delete", "copyTitle": "{{title}} - 复制", "android": "原生安卓（APK）", "mac": "Mac桌面版", "win32": "Win32桌面版", "win64": "Win64桌面版", "installer": "Windows安装程序", "browser": "网页应用程序（浏览器）", "open": "打开", "copyUrl": "复制URL", "newWindow": "新窗口", "MENU_ITEMS": {"menuItems": "菜单项", "categories": "类别", "options": "Options", "newItem": "新菜单项", "preview": "预览", "empty": "游戏类别为空", "notSelected": "未选择游戏类别", "addChild": "添加子实体", "showCommissionFilter": "显示佣金过滤器", "useGridLayout": "使用网格布局", "overlayUrl": "Overlay URL", "gridLayoutPortrait": "网格布局（竖屏）", "gridLayoutPortraitList": "列表", "gridLayoutPortraitEnlargedList": "放大列表", "gridLayoutPortraitGrid": "网格"}, "FORM": {"settings": "设置", "title": "标题", "description": "描述", "template": "模板", "templateSetup": "设置模板", "templateList": "模板列表", "active": "已激活", "activate": "激活", "save": "保存", "back": "背部", "gameLaunch": "游戏发布", "gameLaunchDesktop": "桌面浏览器", "gameLaunchMobile": "移动设备浏览器", "gameLaunchOption": {"modal": "模态", "same-window": "同一窗口", "new-window": "新窗口", "new-three-windows": "New window (max 3 window)"}, "GAME_LAUNCH": {"WINDOW_SIZE": {"title": "Window size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "value": "Value", "type": "Type", "typeOption": {"fixed": "Pixels", "percentage": "Percentage", "ratio": "<PERSON><PERSON>"}}}}, "THEMES": {"cashierURL": "收银台URL", "defaultLanguage": "默认语言", "loginURL": "登录URL", "logoImg": "logo图片 (PNG)", "selectLogo": "选择logo", "selectIcon": "选择图标", "iconImg": "图标图片(PNG)", "iconUrl": "URL", "iconCaption": "Icon caption", "chooseFile": "选择文件", "btnAdd": "添加", "isLogoOnLogin": "在登录页面显示游戏logo", "showUnfinishedGames": "在登录/退出页面上显示未完成的游戏", "customCss": "自定义CSS", "exportSettings": "导出设置JSON", "importSettings": "导入设置JSON", "jsonLoaded": "JSON已成功加载并应用于主题", "jsonEmpty": "JSON已成功加载，但内部没有CSS", "syntaxJsonError": "JSON输入意外结束", "disableLogout": "禁用注销选项", "disableCloseGame": "Disable close game", "numberOfPlayers_show": "Show number of players", "numberOfPlayers_games": "Number of players per game/table", "template": "主题模板", "templateDefault": "默认", "templateFoxy": "主题", "gameName": "Game name", "gameCode": "Game code", "COLORS": {"bgPrimary": "基础背景色", "bgExtra": "附加背景色", "bgHighlight": "高亮元素背景色", "fontPrimary": "基础字体颜色", "fontExtra": "附加字体颜色", "fontHighlight": "高亮元素字体颜色", "borderColor": "边框颜色", "fontHoverColor": "字体/图标悬停颜色", "errorColor": "错误消息颜色", "bgLoginDialogColor": "登录对话框背景颜色", "bgLoginImage": "登录背景图片（PNG）", "bgHomeImage": "主页背景图片（PNG）", "bgHeader": "标题背景颜色", "bgPanel": "面板背景颜色", "bgSidebar": "补充工具栏背景颜色", "bgModal": "模态对话框背景颜色", "bgButtonAdditional": "附加按钮背景颜色", "bgListItemStateHover": "列表项悬停背景颜色", "bgListItemStateActive": "列表项活动背景颜色", "iconViewToggleColor": "查看切换按钮颜色", "bgTriangleColor": "边栏三角形", "shadowBalance": "平衡阴影", "bgScrollBar": "滚动条背景颜色", "bgScrollThumb": "滚动滑块背景颜色", "dialogBackgroundColor": "Dialog background color", "inputsBorderColor": "Inputs border color", "backgroundImage": "Background image (PNG)", "backgroundColor": "Background color", "backgroundColorMobile": "Background color (mobile)", "textColor": "Text color", "hoverTextColor": "Hover text color", "hoverBackgroundColor": "Hover background color", "defaultImage": "Default image", "scoreboardBackgroundColor": "Scoreboard background color (mobile portrait orientation)", "headerTextColor": "Header text color", "timeColor": "Time color", "balanceLabelTextColor": "'Balance' label text color", "balanceValuetextColor": "'Balance' value text color", "balanceLabelTextColorMobile": "'Balance' label text color (mobile)", "balanceValueTextColorMobile": "'Balance' value text color (mobile)", "scrollTrackBackgroundColor": "Scroll track background color", "scrollThumbBackgroundColor": "Scroll thumb background color", "hoverBorderColor": "Hover border color", "activeBorderColor": "Active border color", "closeColor": "Close button color", "closeHoverColor": "Close button hover color", "iconTextColor": "Icon text color", "iconTextHoverColor": "Icon text hover color", "minimisedGameCaptionBg": "Minimised game caption background color", "minimisedGameCaptionColor": "Minimised game caption text color", "minimisedGameBorderColor": "Minimised game border color", "nextButtonHighlightBg": "'Next category' button highlighted background color", "itemHighlightedBgMobile": "Highlighted element background color (mobile)", "color": "Color", "hoverColor": "Hover color", "titleColor": "Title color", "itemTextColor": "Item text color", "limitsPreviewBg": "Limits preview background color", "limitsPreviewColor": "Limits preview text color", "limitsOptionsBg": "Limits options background color", "limitsOptionsItemBg": "Limits options item background color", "limitsOptionsItemColor": "Limits options item text color", "limitsOptionsItemHighlightColor": "Limits options highlighted item text color", "limitsOptionsHeaderColor": "Limits options header text color", "gameItemBg": "Game item background color", "gameItemColor": "Game item text color", "gameItemHighlightedColor": "Game item highlighted text color", "menuBtnColor": "Menu button color", "gameThumbBgImage": "Game thumb background image", "highlightColor": "Highlight color", "categoriesBg": "Categories background color", "thumbBg": "Thumb background color", "bottomBorderColor": "Bottom border color"}, "GROUPS": {"groupLogin": "Login page view", "groupButtonDefault": "Default button view", "groupButtonHighlight": "Highlighted button view", "groupFooter": "Footer view", "groupLanguageSelectBox": "Language select box view", "groupGameThumb": "Game thumb view", "groupLimits": "Limits view", "groupLiveTimer": "Live timer view", "groupPage": "Page view", "groupModal": "Modal view", "groupModalGame": "Modal game view", "groupUnfinished": "Unfinished games view", "groupHeader": "Header view", "groupCategories": "Categories view", "groupMobileMenu": "Mobile menu view", "groupWelcome": "Player welcome view", "groupMobile": "Mobile view", "groupTemplateMobile": "Mobile template view"}, "LAYOUT": {"layoutType": "缩略图大小", "large": "大", "medium": "中", "small": "小"}, "PRELOADER": {"showPreloader": "显示预加载器", "selectLogo": "选择预载logo", "preloaderLogo": "预加载器标识 (PNG,SVG,JPG)", "preloaderAnimation": "预载动画", "disabled": "禁用", "fadeIn": "淡入", "scaleIn": "缩进", "slideLeft": "向左滑动", "slideRight": "向右滑动", "backgroundColor": "预载背景色"}, "REFRESH_LIVE": {"title": "真人赌桌的刷新间隔（秒）", "value1": "15", "value2": "30", "value3": "45", "value4": "60"}, "PLAYER_INACTIVITY": {"title": "玩家不活动超时", "value0": "禁用", "value4": "10分钟", "value5": "15分钟", "value6": "30分钟", "value7": "1小时", "value8": "2小时"}}}, "INTEGRATIONS": {"selectOperator": "选择运营商", "pleaseSelectEntity": "请选择实体", "wallet": "运营商（钱包）", "url": "URL", "walletApi": "iWallet API Documentation", "seamless": "运营商（无缝）", "merchantCode": "商家代码", "merchantPassword": "商家密码", "serverURL": "Server URL", "btnSave": "保存", "btnTest": "测试", "error": "错误", "tests": "测试", "success": "成功", "failures": "失败", "start": "开始", "end": "结束", "duration": "时长", "confirmMessage": "您确定要保存设置吗？", "btnYes": "是", "btnNo": "否", "notificationConfigSaved": "已成功应用设置", "notificationCopy": "已将所选内容复制到剪贴板！", "lastIntegrationsTestsPassed": "Last integrations tests passed", "gameCode": "游戏代码", "merchantType": "Merchant Type", "customId": "Player code", "currencyCode": "Currency code", "ticket": "Ticket", "json": "JSON", "loading": "Loading", "testsPassed": "All tests passed", "testsFailed": "Some tests are failed", "testId": "Test Id", "merchantName": "Merchant Name", "createdAt": "Created At", "details": "Details", "time": "Time", "request": "Request", "response": "Response", "statusCode": "Status Code", "requestBody": "Request Body", "responseBody": "Response Body", "responseTime": "Response Time", "collapse": "Collapse", "expanded": "Expanded", "merchantSettings": "Merchant settings", "testExecutionParameters": "Test execution parameters (optional)", "ticketRequiredCase": "Ticket is required in case the \"get_ticket\" API endpoint was not implemented", "warningMessage": "Integration tests are available only for operators type BRAND or MERCHANT"}, "MARKETING_MATERIALS": {"btnBack": "返回市场营销材料", "seeAll": "查看全部", "seeMore": "查看详情", "GAME_DETAILS": {"btnBack": "返回市场营销材料", "btnGet": "获得", "btnMore": "展开全文", "btnLess": "收起全文", "btnDownload": "下载", "rtp": "玩家回报率", "languages": "语言", "countries": "国家", "currencies": "货币", "tags": "标签", "slot": "老虎机", "packageCurrent": "当前材料包", "packageAll": "所有材料包", "screenshots": "截屏", "overlay": "Overlay", "marketing": "市场营销", "files": "{{number}}份文件", "MODAL": {"getURL": "点击URL来在娱乐模式下玩游戏", "getToken": "游戏令牌", "btnClose": "关闭"}, "GROUPS": {"Characters": "字符", "Fonts": "字体", "Game Logo": "游戏logo", "Icons": "图标", "Posters": "海报", "Promo Video": "推广视频", "Sale Sheet": "销售单页", "Screenshot": "截屏", "Site Icon": "网站图标", "Source file": "源文件", "Symbols": "图案"}}}, "DOMAINS": {"title": "域管理", "addDomain": "添加域", "addPool": "添加池", "addDynamicPool": "添加动态池", "editDomain": "编辑域", "addStaticDomain": "添加静态域", "addDynamicDomain": "添加动态域", "editStaticDomain": "编辑静态域", "editDynamicDomain": "编辑动态域", "removeMessage": "Do you really want to remove domain?", "staticDomainPools": "静态域池", "dynamicDomainPools": "动态域池", "staticDomains": "静态域", "dynamicDomains": "动态域", "notificationCreated": "已成功创建域\"{{domain}}\" ", "notificationModified": "已成功修改域\"{{domain}}\" ", "notificationRemoved": "已成功移除域\"{{domain}}\" ", "GRID": {"created": "已创建", "domain": "域", "editDomain": "编辑域", "environment": "环境", "gameServer": "Game server", "removeDomain": "移除域", "serverDescription": "Server description", "updated": "已更新"}}, "PROXY": {"title": "代理管理", "addNew": "添加新代理", "addProxy": "添加新代理", "editProxy": "编辑代理", "removeProxy": "移除代理", "notificationCreated": "已成功创建代理\"{{proxy}}\" ", "notificationModified": "已成功修改代理\"{{proxy}}\" ", "notificationRemoved": "已成功移除代理\"{{proxy}}\" ", "notificationProxyRemove": "您是否确定要移除代理: {{proxy}}?", "GRID": {"created": "已创建", "updated": "已更新", "url": "URL", "description": "描述"}}, "GRC_BLOCK": {"title": "挑战管理", "create": "创建挑战", "notificationRemove": "您确定要移除挑战吗？", "notificationChallengeRemoved": "已成功移除{{challengeId}}", "notificationCreated": "已成功创建挑战！", "notificationConfigSaved": "已成功保存配置！", "newChallengePlaceholder": "将新的挑战JSON文件放到此处……", "saveConfig": "保存配置", "endChallenge": "结束挑战", "namePlaceholder": "Enter challenge name", "potAmountPlaceholder": "Enter pot amount", "targetBadgesPlaceholder": "Enter target badges", "logoUrlPlaceholder": "Enter logo URL", "GRID": {"id": "ID", "name": "名称", "state": "状态", "potAmount": "奖池金额", "targetBadges": "目标徽章", "logoUrl": "Logo的URL地址", "startAt": "开始于", "finishAt": "结束于", "actions": "操作", "currency": "货币"}, "EMAIL": {"emailManagement": "邮件管理", "titleStart": "通知挑战开始的邮件", "titleEnd": "通知挑战取消的邮件", "notificationEmailSendSuccess": "已成功发送邮件！ ", "notificationEmailGetSuccess": "已成功接收邮件模板！", "tournamentCanceled": "锦标赛已被取消", "FORM": {"name": "发件人", "nameEn": "挑战的英文名称", "nameCh": "挑战的中文名称", "recipients": "收件人（每行一个）", "subject": "主题", "template": "邮件模板", "btnGetEmail": "获得邮件模板", "btnSendEmail": "发送邮件"}}, "DEFINITION": {"editDefinition": "编辑定义", "notificationDefinitionUpdated": "定义已成功更新", "logoUrl": "标识URL"}}, "GAME_SERVER": {"title": "游戏服务器设置", "editForm": "编辑表格", "createForm": "创建表格", "createNew": "创建新游戏服务器", "editServer": "编辑服务器", "removeServer": "移除服务器", "messageRemove": "您是否确定要移除游戏服务器配置？", "notificationConfigSaved": "已成功保存配置！", "notificationConfigAdded": "已成功添加配置！", "notificationConfigRemoved": "已成功移除配置！", "configPlaceholder": "将新的游戏服务器配置JSON文件放到此处……", "GRID": {"name": "名称", "description": "描述", "roundIdRange": "局ID范围", "sessionIdRange": "环节ID范围", "createdAt": "已创建", "modifiedAt": "已修改"}}, "BULK_ACTIONS": {"dynamicDomains": "动态域", "staticDomains": "静态域", "messageEqualizeDynamic": "动态域名将继承自母实体", "messageEqualizeStatic": "动态域名将继承自母实体", "messageChangeDynamic": "动态域将更改为 <b>{{value}}</b>", "messageChangeStatic": "静态域将更改为<b>{{value}}</b>", "messageSwitchEnvironment": "所选域位于不同的环境中。请现在\"实体设置\"页面切换环境", "entitySettings": "实体设置页面", "notificationUpdate": "已成功完成批量更新", "notify": "Please, specify one of these parameters to proceed with search: Name, Server, Domain or press \"Show all\" button", "btnShowAll": "Show all", "btnDownloadCsv": "Download changes", "switchAllConfirm": "All previous changes will be cancelled. Are you sure?"}, "GAME": {"gameNameLabel": "Game Name", "gameCode": "Game Code", "gameUrl": "Game URL", "titleCreateGame": "Create Game", "titleEditGame": "Edit Game", "titlePlaceholder": "Enter Game title", "gameCodePlaceholder": "Enter Game code", "providerGameCodePlaceholder": "Enter Provider game code", "urlPlaceholder": "Enter Game URL", "totalBetMultiplier": "Total Bet Multiplier", "totalBetMultiplierPlaceholder": "Enter Total Bet Multiplier", "slot": "Slot", "action": "Action", "table": "Table", "GRID": {"providerTitle": "Game Provider", "title": "Game Name", "code": "Game Code", "status": "Status", "statusActive": "Active", "statusInactive": "Inactive", "category": "Category", "slotCategory": "Slot", "tableCategory": "Table game", "rouletteCategory": "Roulette", "type": "Type", "typeHtml5": "Html5", "typeFlash": "Flash", "typeDownloadable": "Downloadable", "features": "Features", "progressiveFeature": "Progressive", "jackpotFeature": "Jackpot", "brandedFeature": "Branded", "historyRenderType": "Render Type", "limitFiltersWillBeApplied": "Game Limits Filter", "limitFiltersAvailable": "available", "limitFiltersUnavailable": "unavailable"}, "GENERAL": {"historyUrlLabel": "Game history URL", "historyRenderTypeLabel": "History render type", "title": "General", "typeLabel": "Type", "providerLabel": "Provider", "notSet": "Not set", "countriesLabel": "Countries", "schemaDefinitionLabel": "Schema definition"}, "LIMITS": {"title": "Limits", "limitsPlaceholder": "Put limits JSON here", "featuresPlaceholder": "Put features JSON here"}, "FEATURE": {"title": "Features", "featuresPlaceholder": "Put features JSON here", "transferEnabled": "Transfer enabled"}, "SETTINGS": {"title": "Settings", "settingsPlaceholder": "Put settings JSON here"}, "CLIENT_FEATURES": {"title": "Client features", "clientFeaturesPlaceholder": "Put client features JSON here"}}, "MERCHANT": {"PARAMETERS": {"gsId": "识别POP环境下的远程游戏服务器", "gpId": "识别赌场品牌", "supportTransfer": "动作游戏支持", "supportForceFinishAndRevert": "能够强制结束游戏局", "forceFinishAndRevertInSWWalletOnly": "能够在内部钱包中强制结束游戏局", "serverUrl": "服务器URL", "password": "密码", "username": "用户名", "sameUrlForTerminalLoginAndTicket": "使用常规登录方法登录终端", "isUnderAAMSRegulation": "根据AAMS法规", "gameLogoutOptions": {"common": "游戏登出选项", "type": "类别", "maxRetryAttempts": "重试次数上限", "maxSessionTimeout": "游戏暂停时长上限", "unfinished": "未结束", "all": "全部"}, "isPromoInternal": "内部推广", "walletPerGame": "每个游戏的钱包", "ignoreBalanceRequestError": "忽略余额请求错误", "supportPlayMoney": "支持娱乐资金"}}, "JURISDICTION": {"title": "Juris<PERSON>", "jurisdictionRemoved": "Jurisdiction was removed", "confirmationRemoved": "Do you really want to delete this jurisdiction?", "notificationAdded": "Jurisdiction was successfully added", "notificationChanged": "Jurisdiction was successfully changed", "GRID": {"title": "Juris<PERSON>", "code": "Code", "description": "Description", "defaultCountry": "Default Country", "allowedCountries": "Allowed Countries", "restrictedCountries": "Restricted Countries", "actionEdit": "Edit", "actionRemove": "Delete"}, "MODAL": {"code": "Code", "title": "Title", "countries": "Countries", "description": "Description", "titleEdit": "Edit jurisdiction", "defaultCountry": "Default Country", "titleCreate": "Create jurisdiction", "allowedCountries": "Allowed Countries", "countriesListType": "Countries List Type", "restrictedCountries": "Restricted Countries"}}, "LABEL_MANAGEMENT": {"title": "Labels Management", "createLabel": "Create Label", "createLabelGroup": "Create Label Group", "confirmationRemoved": "Do you really want to delete \"{{name}}\" label?", "notificationCreated": "Label was created successfully", "notificationGroupCreated": "Label Group was created successfully", "notificationRemoved": "Label was removed", "hintText": "Only labels of the \"Entity\" type can be deleted.", "GRID": {"title": "Title", "group": "Group", "type": "Type", "relationType": "Relation Type", "created": "Created", "updated": "Updated", "delete": "Delete"}, "MODAL": {"title": "Title", "titleCreate": "Create new Label", "titleGroupCreate": "Create new Label Group", "groupType": "Group type", "group": "Group", "groupName": "Group name", "relationType": "Relation Type"}, "TYPES": {"game": "Game", "entity": "Entity", "promotion": "Promotion", "oneToOne": "One to One", "oneToMany": "One to Many"}}, "BI": {"reportNotFound": "无法找到该报告。 请联系客服"}, "GAME_LIMITS": {"addButtonTitle": "Add", "currency": "<PERSON><PERSON><PERSON><PERSON>", "defaultGamePlaceholder": "Default game", "entity": "Entity", "gameGroup": "Game Group", "gameType": "Game Type", "game": "Game", "gameLimitsChangedMessage": "Game limits successfully changed", "gameLimitsNotFoundMessage": "Game limits configuration not found", "readConfigButtonTitle": "Show Custom Limits", "removeButtonTitle": "Remove", "schema": "<PERSON><PERSON><PERSON>", "selectAllButtonTitle": "Select All", "submitButtonTitle": "Submit", "title": "Title", "titlePlaceholder": "Enter title of game limits configuration", "slotLimitsTab": "Slots/Table/Arcade", "liveLimitsTab": "Live Games", "LIVE": {"baseCurrency": "Base Currency", "operatorCurrencies": "Operator Currencies", "expandCollapseAll": "Expand/Collapse All", "createLevel": "Create Level", "addLevel": "Add Level", "selectLevel": "Select Level", "currency": "<PERSON><PERSON><PERSON><PERSON>", "level": "Level", "visibility": "Visibility", "default": "<PERSON><PERSON><PERSON>", "visible": "Visible", "actions": "Actions", "betType": "Bet Type", "payout": "Payout", "min": "Min", "max": "Max", "positionMax": "Position Max", "exposure": "Exposure", "alert": "<PERSON><PERSON>", "block": "Block", "defaultLimitsConfiguration": "Default Limits Configuration", "stakeAll": "Stake All", "stakeDef": "<PERSON><PERSON> Def", "stakeMax": "Stake Max", "stakeMin": "Stake Min", "totalStakeMax": "Total Stake Max", "totalStakeMin": "Total Stake Min", "isDefaultRoom": "Is Room Default", "concurrentPlayers": "Estimated concurrent players", "order": "Order", "customLimits": "Custom Limits", "inheritedLimits": "Inherited Limits", "alignedToEuro": "Aligned to Euro", "updateLimitsMessage": "\"{{currency}}\" limits of \"{{level}}\" level were successfully changed", "changeLevelVisibilityMessage": "Do you really want to change visibility of \"{{level}}\" level?", "levelVisibilityChangedMessage": "Visibility of \"{{level}}\" level was successfully changed", "changeDefaultLevelMessage": "Do you really want to set \"{{level}}\" level as default?", "levelSetAsDefaultMessage": "\"{{level}}\" level was successfully set as default", "resetLimitsMessage": "Do you really want to reset \"{{currency}}\" limits of \"{{level}}\" level to inherited values?", "limitsSuccessfullyReset": "\"{{currency}}\" limits of \"{{level}}\" level were successfully reset to inherited values", "limitsAlreadyInheritedMessage": "\"{{currency}}\" limits of \"{{level}}\" level is already inherited", "deleteLimitsMessage": "Do you really want to delete \"{{level}}\" level", "levelSuccessfullyDeleted": "\"{{level}}\" level was successfully deleted", "duplicateLimitsMessage": "\"{{currency}}\" limits of \"{{level}}\" level were successfully duplicated", "removeLevel": "Do you really want to make \"{{level}}\" level for \"{{currency}}\" invisible for the end user?", "editLimits": "Edit limits", "renameLevelTooltip": "Rename level", "renameLevel": "Rename \"{{title}}\" level", "levelRenamedMessage": "Level was successfully renamed", "resetLimits": "Reset limits to inherited", "deleteLevel": "Delete level", "duplicateLimits": "Duplicate Limits", "createNewLevel": "Create New Level", "addLevelToLimitsConfiguration": "Add An Existing Level To The Limits Configuration", "title": "Title", "levelsSortOrder": "Levels sort order", "descSortOption": "High to low", "ascSortOption": "Low to high", "levelAlreadyExistsInConfiguration": "This level already exists in the limits configuration", "levelAlreadyExists": "This level already exists", "entirelyInheritedMessage": "There is no possibility to remove level from the limits configuration, which is entirely inherited"}}, "LANGUAGES": {"en": "English (EN)", "zh": "中文 (ZH)", "zh-tw": "繁體中文 (ZH-TW)", "ja": "日本語 (JA)", "ms": "Bahasa Malaysia (MS)", "ko": "한국어 (KO)", "th": "ภาษาไทย (TH)", "vi": "<PERSON><PERSON><PERSON><PERSON> (VI)", "id": "Bahasa Indonesia (ID)", "ro": "Român<PERSON> (RO)", "it": "<PERSON><PERSON> (IT)", "el": "Ελληνικά (EL)", "km": "កម្ពុជា (KM)", "es": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ES)", "pt": "<PERSON><PERSON><PERSON><PERSON><PERSON> (PT)", "pt-br": "<PERSON>ug<PERSON><PERSON><PERSON> (PT-BR)", "ru": "Русский (RU)", "de": "<PERSON><PERSON><PERSON> (DE)", "sv": "Svenska (SV)", "da": "Dansk (DA)", "nl": "Nederlands (NL)", "bg": "Български (BG)", "sr": "<PERSON><PERSON><PERSON> (SR)", "tr": "Türkçe (TR)", "ENGLISH": {"en": "English (EN)", "th": "Thai (TH)", "vi": "Vietnamese (VI)", "id": "Bahasa Indonesian (ID)", "zh-cn": "Simplified Chinese (ZH-CN)", "ko": "Korean (KO)", "ro": "Romanian (RO)", "it": "Italian (IT)", "es": "Spanish (ES)", "el": "Greek (EL)", "km": "Cambodian (KM)", "pt": "Portuguese (PT)", "pt-br": "Brazilian Portuguese (PT-BR)", "ru": "Russian (RU)", "de": "German (DE)", "sv": "Swedish (SV)", "da": "Danish (DA)", "nl": "Dutch (NL)", "bg": "Bulgarian (BG)", "sr": "Serbian (SR)", "tr": "Turkish (TR)"}}, "TITLE": "Skywind Back-office / Casino", "HUBS": {"analytics": "Analytics", "casino": "Casino", "engagement": "Engagement", "studio": "Live Studio"}, "BI-REPORTS-SWITCH": {"pid": "PID", "baseUrl": "Base Url", "trustServerUrl": "Trust Server Url", "successSaveMessage": "BI reports {{pair}} was saved successfully"}}